import Header from '../Header/Header';
import './FeaturesHero.css';


const FeaturesHero = () => {
  return (
    <section className="features-hero-section">

      <div className="features-hero-container">
        <Header />
        <h2 className="features-hero-title">
          <span className="features-hero-orange-text">Powerful Features to </span>
          <span className="features-hero-gradient-text">Manage Clients</span><br />
          <span className="features-hero-orange-text">Like a Pro</span>
        </h2>

        <p className="features-hero-description">
          Celaeno Client Manager brings together everything agencies, consultants, and service businesses need to
          deliver outstanding client experiences from onboarding to invoicing, proposals to collaboration.
        </p>
        <div className="cta-container">
          <button className="lets-go-btn">
            Sign Up
          </button>
          <p className="free-trial-text">Start your free trial - no credit card needed!</p>
        </div>
      </div>
    </section>
  );
};

export default FeaturesHero;
