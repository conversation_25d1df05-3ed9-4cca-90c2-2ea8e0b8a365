import React from 'react';
import { Link } from 'react-router-dom';
import featuresData from '../../data/featuresData';
import './FeaturesDropdown.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGlobe,
  faMobileAlt,
  faCode,
  faShoppingCart,
  faPaintBrush,
  faRobot,
  faLifeRing,
  faSync,
  faExchangeAlt,
  faWrench,
  faHeadset,
  faBullhorn,
  faSearch,
  faShareAlt,
  faMousePointer,
  faEnvelopeOpenText,
  faBoxOpen,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';

const FeaturesDropdown = ({ isOpen, onMouseEnter, onMouseLeave }) => {
  // Define icons for each feature category
  const appDevIcons = [faGlobe, faMobileAlt, faCode, faShoppingCart, faPaintBrush, faRobot];
  const solutionIcons = [faLifeRing, faSync, faExchangeAlt, faWrench, faHeadset, faSync];
  const marketingIcons = [faBullhorn, faSearch, faShareAlt, faMousePointer, faEnvelopeOpenText, faBoxOpen];

  // Get the first 10 features from our data store
  const featureCards = featuresData.slice(0, 10).map((feature, index) => {
    let icon;
    if (index < 3) {
      icon = appDevIcons[index];
    } else if (index < 6) {
      icon = solutionIcons[index - 3];
    } else {
      icon = marketingIcons[index - 6];
    }

    return {
      id: feature.id,
      title: feature.title,
      icon: icon
    };
  });

  // Group features into 3 columns
  const applicationDevelopment = featureCards.slice(0, 3);
  const solution = featureCards.slice(3, 6);
  const digitalMarketing = featureCards.slice(6, 10);

  return (
    <div
      className={`features-dropdown ${isOpen ? 'open' : ''}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="features-dropdown-container">
        <div className="features-dropdown-columns">
          <div className="features-column">
            <ul className="features-list">
              {applicationDevelopment.map((feature) => (
                <li key={feature.id}>
                  <Link
                    to={`/featuresdetails/${feature.id}`}
                    className="feature-link"
                    onClick={onMouseLeave}
                  >
                    <div className="feature-icon">
                      <FontAwesomeIcon icon={feature.icon} />
                    </div>
                    <span>{feature.title}</span>
                    <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="features-column">
            <ul className="features-list">
              {solution.map((feature) => (
                <li key={feature.id}>
                  <Link
                    to={`/featuresdetails/${feature.id}`}
                    className="feature-link"
                    onClick={onMouseLeave}
                  >
                    <div className="feature-icon">
                      <FontAwesomeIcon icon={feature.icon} />
                    </div>
                    <span>{feature.title}</span>
                    <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="features-column">
            <ul className="features-list">
              {digitalMarketing.map((feature) => (
                <li key={feature.id}>
                  <Link
                    to={`/featuresdetails/${feature.id}`}
                    className="feature-link"
                    onClick={onMouseLeave}
                  >
                    <div className="feature-icon">
                      <FontAwesomeIcon icon={feature.icon} />
                    </div>
                    <span>{feature.title}</span>
                    <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesDropdown;
