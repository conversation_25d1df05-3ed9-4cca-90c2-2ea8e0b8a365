import React, { useRef, useEffect } from 'react';
import './TalkToSales.css';
import Header from '../Header/Header';

const TalkToSales = () => {
  // Animate cards and form on scroll in
  const salesRef = useRef();

  useEffect(() => {
    const section = salesRef.current;
    const handleScroll = () => {
      if (section) {
        const rect = section.getBoundingClientRect();
        if (rect.top < window.innerHeight - 50) {
          section.classList.add('in-view');
        }
      }
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section className="sales-section" ref={salesRef}>
       <Header />
      <div className="sales-wave-top">
        <svg viewBox="0 0 1440 120" preserveAspectRatio="none">
          <path
            fill="#00736c"
            d="M0,60 C360,0 1080,120 1440,60 L1440,120 L0,120 Z"
          />
        </svg>
      </div>

      <div className="sales-container">
        <div className="sales-content">
          <h1 className="sales-title">Talk to Sales</h1>
          <p className="sales-desc">
            Ready to simplify your workflows and scale your business? Fill out the form and our Sales team will reach out for a personalized demo and answers to your questions.
          </p>
          <form className="sales-form" autoComplete="off" onSubmit={e => e.preventDefault()}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="name">Full Name</label>
                <input type="text" id="name" placeholder="Your Full Name" required />
              </div>
              <div className="form-group">
                <label htmlFor="email">Business Email</label>
                <input type="email" id="email" placeholder="<EMAIL>" required />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="company">Company Name</label>
                <input type="text" id="company" placeholder="Your Company" required />
              </div>
              <div className="form-group">
                <label htmlFor="team">Team Size</label>
                <select id="team" required>
                  <option value="">Select</option>
                  <option value="1-5">1-5</option>
                  <option value="6-20">6-20</option>
                  <option value="21-50">21-50</option>
                  <option value="51+">51+</option>
                </select>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="message">What can we do to assist you ?</label>
              <textarea id="message" rows="4" placeholder="Let us know your goals or questions..." />
            </div>
            <button className="sales-submit" type="submit">
              <span>Request a Call</span>
              <svg width="22" height="22" viewBox="0 0 22 22" className="btn-arrow">
                <path
                  d="M6 11h10M15 7l4 4-4 4"
                  stroke="#fff"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
              </svg>
            </button>
          </form>
        </div>
        <div className="sales-aside">
          <div className="sales-card">
            <h2>Why Talk to Sales?</h2>
            <ul>
              <li>Personalized walkthrough of Celaeno</li>
              <li>Get pricing for your needs</li>
              <li>Exclusive onboarding tips</li>
              <li>No obligation, just answers!</li>
            </ul>
          </div>
          <div className="sales-contact-card">
            <h3>Or reach us directly</h3>
            <p>
              Email: <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            
          </div>
        </div>        
      </div>

      <div className="sales-wave-bottom">
        <svg viewBox="0 0 1440 120" preserveAspectRatio="none">
          <path
            fill="#00736c"
            d="M0,60 C360,120 1080,0 1440,60 L1440,0 L0,0 Z"
          />
        </svg>
      </div>
    </section>
  );
};

export default TalkToSales;
