
import "./HeroSection.css";
import Hader from "../Header/Header";

const HeroSection = () => {
  return (
    <div className="hero-wrapper">
      <section className="hero">
        <Hader />
        <div className="hero-content">
          <h1>
            <span className="gradient-text">Manage Clients, Projects & Teams in</span><br />
            <span className="gradient-text">One Beautiful Workspace</span>
          </h1>
          <p>
            Say goodbye to juggling spreadsheets, email threads, and scattered tools.
            Celaeno Client Manager brings together everything you need to manage clients,
            onboard faster, send invoices, collaborate with teams, and grow your business effortlessly.
          </p>
          <div className="cta-container-home">
            <a target='_blank' href="https://cms.celaenotechnology.com/register.php" className="lets-go-btn-home">Sign Up</a>
            <p className="free-trial-text">Start your free trial - no credit card needed!</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HeroSection;
