.aio-section {
  width: 100%;
  background: linear-gradient(to bottom, #ffffff, #fef1e5);
  padding: 60px 20px;
  box-sizing: border-box;
}

.aio-container {
  max-width: 1200px;
  margin: 0 auto;
}

.aio-header {
  text-align: center;
  margin-bottom: 50px;
}

.aio-title {
  font-size: 36px;
  color: #00736c;
  margin: 0 0 20px 0;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.aio-header:hover .aio-title {
  transform: scale(1.02);
}

.aio-description {
  font-size: 16px;
  color: #333;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  transition: opacity 0.3s ease;
}

.aio-header:hover .aio-description {
  opacity: 0.9;
}

.aio-cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 40px;
}

.aio-card {
  background-color: white;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  min-height: 200px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid transparent;
  overflow: hidden;
}

.aio-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 115, 108, 0.2);
}

.aio-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #00736c, #4db6ac);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.aio-card:hover::before {
  transform: scaleX(1);
}

.aio-card-title {
  color: #00736c;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
  transition: color 0.3s ease;
}

.aio-card:hover .aio-card-title {
  color: #005b55;
}

.aio-card-content {
  margin-bottom: 20px;
  flex-grow: 1;
}

.aio-card-description {
  color: #000000;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
  transition: transform 0.3s ease;
}

.aio-card:hover .aio-card-description {
  transform: translateX(3px);
}

.aio-card-details {
  color: #333333;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  transition: opacity 0.3s ease;
}

.aio-card:hover .aio-card-details {
  opacity: 0.9;
}

.aio-learn-more {
  color: #00736c;
  font-size: 13px;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
  margin-top: auto;
  transition: all 0.3s ease;
  position: relative;
}

.aio-learn-more:hover {
  color: #f8972a;
}

.aio-arrow-icon {
  margin-left: 5px;
  font-size: 10px;
  transition: transform 0.3s ease;
}

.aio-learn-more:hover .aio-arrow-icon {
  transform: translateX(4px);
}

/* Special layout for the 10th card which is centered */
.aio-cards-grid > .aio-card:nth-child(10) {
  grid-column: 2;
}

/* Card hover spotlight effect */
.aio-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 80%);
  opacity: 0;
  transform: scale(0.5);
  transition: transform 0.6s ease, opacity 0.6s ease;
  pointer-events: none;
}

.aio-card:hover::after {
  opacity: 0.05;
  transform: scale(1);
}

/* Responsive design */
@media (max-width: 992px) {
  .aio-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Reset the special layout for 10th card on smaller screens */
  .aio-cards-grid > .aio-card:nth-child(10) {
    grid-column: auto;
  }
}

@media (max-width: 768px) {
  .aio-section {
    padding: 50px 20px;
  }
  
  .aio-title {
    font-size: 30px;
  }
  
  .aio-description {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .aio-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .aio-section {
    padding: 40px 15px;
  }
  
  .aio-title {
    font-size: 26px;
  }
  
  .aio-description {
    font-size: 14px;
  }
  
  .aio-card {
    padding: 20px;
    min-height: 180px;
  }
}
