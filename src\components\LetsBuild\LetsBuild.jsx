import React from 'react';
import './LetsBuild.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChartPie, faHandshake, faSync, faChartLine } from '@fortawesome/free-solid-svg-icons';

const LetsBuild = () => {
  return (
    <section className="lets-build-section">
      <div className="lets-build-container">
        <div className="lets-build-header">
          <div className="lets-build-title">Let's Build Success Together</div>
          <p className="lets-build-subtitle">
            Looking for a strategic partnership to combine efforts, share resources, and drive profits? You're in the right place!
          </p>
        </div>

        <div className="partnership-models">
          <div className="partnership-model">
            <div className="partnership-icon">
              <FontAwesomeIcon icon={faChartPie} />
            </div>
            <div className="partnership-content">
              <h3 className="partnership-title">Shareholding</h3>
              <p className="partnership-description">
                Reduce software development and maintenance costs with exclusive partnership rates in exchange for company shares.
              </p>
            </div>
          </div>

          <div className="partnership-model">
            <div className="partnership-icon">
              <FontAwesomeIcon icon={faHandshake} />
            </div>
            <div className="partnership-content">
              <h3 className="partnership-title">Joint Venture</h3>
              <p className="partnership-description">
                Start a new business or subsidiary with us as your technology partner, leveraging our expertise for long-term success.
              </p>
            </div>
          </div>

          <div className="partnership-model">
            <div className="partnership-icon">
              <FontAwesomeIcon icon={faSync} />
            </div>
            <div className="partnership-content">
              <h3 className="partnership-title">Dedicated Development Center (DDC)</h3>
              <p className="partnership-description">
                Establish your own legal entity in Ukraine, tapping into outsourcing benefits while relying on our knowledge and experience.
              </p>
            </div>
          </div>

          <div className="partnership-model">
            <div className="partnership-icon">
              <FontAwesomeIcon icon={faChartLine} />
            </div>
            <div className="partnership-content">
              <h3 className="partnership-title">Sales Partnership</h3>
              <p className="partnership-description">
                Perfect for independent sellers or third-party companies seeking a trusted service provider to ensure high-quality, on-time project delivery.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LetsBuild;