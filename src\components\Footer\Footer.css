
.footer {
  position: relative;
  color: #000000;
  padding: 50px 0 30px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden; /* For animations that might extend beyond */
}

@keyframes gradientShift {
  0% { background-color: rgba(255, 255, 255, 0.4); }
  50% { background-color: rgba(255, 255, 255, 0.5); }
  100% { background-color: rgba(255, 255, 255, 0.4); }
}

.footer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4);
  z-index: 1;
  animation: gradientShift 8s infinite ease-in-out;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.footer-top {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 40px;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.footer-column {
  flex: 1;
  min-width: 200px;
  margin-bottom: 30px;
  padding-right: 20px;
  animation: slideUp 0.6s ease-out backwards;
}

.footer-column:nth-child(1) {
  animation-delay: 0.1s;
}

.footer-column:nth-child(2) {
  animation-delay: 0.2s;
}

.footer-column:nth-child(3) {
  animation-delay: 0.3s;
}

.footer-column:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes logoFloat {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

.footer-logo {
  margin-bottom: 20px;
  position: relative;
}

.footer-logo::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #000, transparent);
  transition: width 0.5s ease;
}

.footer-logo:hover::after {
  width: 80px;
}

.cms-logo {
  margin-left: 63px;
  height: 50px;
  width: auto;
  margin-bottom: 5px;
  transition: all 0.3s ease;
  animation: logoFloat 3s ease-in-out infinite;
}

.footer-logo:hover .cms-logo {
  transform: scale(1.05);
}

.footer-description {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #000000;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  position: relative;
  font-style: italic;
  padding: 10px 15px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(0, 115, 108, 0.05));
  border-radius: 10px;
  border-left: 3px solid #00736c;
}

.quote-mark {
  font-family: 'Georgia', serif;
  font-size: 24px;
  font-weight: bold;
  color: #00736c;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 115, 108, 0.3);
  position: relative;
  display: inline-block;
}

.quote-mark.opening {
  margin-right: 5px;
  animation: fadeInLeft 0.8s ease-out;
}

.quote-mark.closing {
  margin-left: 5px;
  animation: fadeInRight 0.8s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  color: #000000;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  font-size: 18px;
}

.social-icons a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-icons a:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-5px) rotate(5deg);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  color: #00736c;
}

.social-icons a:hover::before {
  opacity: 1;
}

.social-icons a:nth-child(1) { animation: pulse 3s infinite 0.2s; }
.social-icons a:nth-child(2) { animation: pulse 3s infinite 0.4s; }
.social-icons a:nth-child(3) { animation: pulse 3s infinite 0.6s; }
.social-icons a:nth-child(4) { animation: pulse 3s infinite 0.8s; }

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0px;
}

.footer-links a{
  margin-bottom: 10px;
}

.footer-links a {
  color: #000000;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  padding-left: 0;
  display: inline-block;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.footer-links a::before {
  content: '→';
  position: absolute;
  left: -20px;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: #00736c;
  padding-left: 5px;
  transform: translateX(8px);
}

.footer-links a:hover::before {
  opacity: 1;
  left: -15px;
}

@keyframes borderGlow {
  0% { border-color: rgba(0, 0, 0, 0.15); }
  50% { border-color: rgba(0, 115, 108, 0.3); }
  100% { border-color: rgba(0, 0, 0, 0.15); }
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.15);
  position: relative;
  animation: borderGlow 8s infinite ease-in-out;
}
.footer-title {
  margin: 0 0 20px 0;
  color: #000000;
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #000, transparent);
  transition: width 0.5s ease;
}

.footer-title:hover::after {
  width: 80px;
}

/* Add left padding to Product section */
.footer-column:nth-child(2) {
  padding-left: 80px;
}
.footer-column:nth-child(4) {
  padding-left: 30px;
}

.copyright {
  font-size: 14px;
  color: #000000;
  margin: 0;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.legal-links {
  display: flex;
  gap: 20px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.legal-links a {
  font-size: 14px;
  color: #000000;
  text-decoration: none;
  transition: all 0.3s;
  position: relative;
  padding: 3px 8px;
  border-radius: 4px;
  animation: fadeIn 0.5s ease-out backwards;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.3);
}

.legal-links a:nth-child(1) {
  animation-delay: 0.1s;
}

.legal-links a:nth-child(2) {
  animation-delay: 0.2s;
}

.legal-links a:hover {
  color: #00736c;
  background-color: rgba(0, 115, 108, 0.1);
}

@media (max-width: 992px) {
  .footer-column {
    flex: 0 0 calc(50% - 20px);
  }
}

@media (max-width: 576px) {
  .footer {
    padding: 50px 0 25px;
  }
  .footer-column:nth-child(2) {
  padding-left: 0px;
}
.footer-column:nth-child(4) {
  padding-left: 0px;
}
  .footer-column {
    flex: 0 0 100%;
    padding-right: 0;
  }

  .footer-logo {
    display: flex;
    justify-content: center;
  }

  .cms-logo {
    height: 35px;
  }

  .footer-description {
    text-align: center;
    padding: 8px 12px;
    font-size: 13px;
  }

  .quote-mark {
    font-size: 20px;
  }

  .social-icons {
    justify-content: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .legal-links {
    justify-content: center;
  }

  .footer-overlay {
    background-color: rgba(255, 255, 255, 0.5);
  }
}