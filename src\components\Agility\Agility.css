/* Agility Section */
.agility-section {
  position: relative;
  background-color: #f5f5f5;
  padding: 80px 0;
  overflow: hidden;
}

.agility-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.agility-header {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.agility-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.2;
}

.highlight-text {
  color: #007474;
  position: relative;
  display: inline-block;
}

.agility-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
}

/* Steps Section */
.agility-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
}

.agility-step {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  position: relative;
}

.agility-step:nth-child(even) {
  margin-left: auto;
}

.agility-step:nth-child(odd) {
  margin-right: auto;
}

.step-icon {
  background-color: #007474;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-description {
  font-size: 1rem;
  color: #333;
  line-height: 1.6;
  margin: 0;
  font-weight: 500;
}

/* Curved Background */
.curved-background {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: url('../../assets/Rectangle 82.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
  opacity: 0.8;
}

/* Desktop Layout */
@media (min-width: 768px) {
  .agility-steps {
    flex-direction: column;
    gap: 40px;
  }

  .agility-step {
    max-width: 500px;
  }

  .agility-step:nth-child(1) {
    margin-left: 0;
    margin-right: auto;
  }

  .agility-step:nth-child(2) {
    margin-left: auto;
    margin-right: 0;
  }

  .agility-step:nth-child(3) {
    margin-left: 0;
    margin-right: auto;
  }

  .agility-step:nth-child(4) {
    margin-left: auto;
    margin-right: 0;
  }
}

/* Large Desktop */
@media (min-width: 1200px) {
  .agility-section {
    padding: 100px 0;
  }

  .agility-title {
    font-size: 4rem;
  }

  .agility-description {
    font-size: 1.2rem;
  }

  .agility-step {
    max-width: 550px;
    padding: 35px;
  }

  .step-icon {
    width: 60px;
    height: 60px;
    font-size: 1.4rem;
  }

  .step-description {
    font-size: 1.1rem;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .agility-section {
    padding: 60px 0;
  }

  .agility-container {
    padding: 0 15px;
  }

  .agility-header {
    margin-bottom: 40px;
    text-align: left;
  }

  .agility-title {
    font-size: 2.5rem;
    text-align: left;
    margin-bottom: 15px;
  }

  .agility-description {
    font-size: 1rem;
    text-align: left;
  }

  .agility-steps {
    gap: 25px;
  }

  .agility-step {
    max-width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 25px;
    gap: 15px;
  }

  .step-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .step-description {
    font-size: 0.95rem;
  }

  .curved-background {
    opacity: 0.6;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .agility-section {
    padding: 50px 0;
  }

  .agility-container {
    padding: 0 10px;
  }

  .agility-title {
    font-size: 2rem;
  }

  .agility-description {
    font-size: 0.95rem;
  }

  .agility-step {
    padding: 20px;
  }

  .step-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .step-description {
    font-size: 0.9rem;
  }
}