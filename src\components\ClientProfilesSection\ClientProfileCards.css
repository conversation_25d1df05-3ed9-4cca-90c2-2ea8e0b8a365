/* ClientProfileCards.css - Updated to match design exactly */
.profile-section {
  width: 100%;
  padding: 80px 20px;
  padding-bottom: 150px;
  box-sizing: border-box;
  background-color: #fff;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 80px;
}

.profile-row {
  position: relative;
  min-height: 180px;
  display: flex;
  margin-bottom: 20px;
}

.profile-row-odd {
  justify-content: flex-start;
}

.profile-row-even {
  justify-content: flex-end;
}

/* Shape styling to match the image */
.profile-shape {
  width: 400px;
  height: 280px;
  border-radius: 25px;
  transform: rotate(-5deg);
  position: absolute;
  z-index: 1;
}

.profile-row-odd .profile-shape {
  left: 0;
  top: 0;
}

.profile-row-even .profile-shape {
  right: 0;
  top: 0;
}

.profile-shape-teal {
  background-color: #4DB6AC;
}

.profile-shape-orange {
  background-color: #FF9E57;
}

/* Card styling to match the image */
.profile-card {
  width: 60%;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
}

.profile-card-content {
  border-radius: 12px;
  padding: 25px 30px;
  
}

/* Position cards correctly */
.profile-row-odd .profile-card {
  margin-left: 550px;
}

.profile-row-even .profile-card {
  margin-right: 550px;
}

.profile-title {
  font-size: 22px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.profile-title-teal {
  color: #00736C;
}

.profile-title-orange {
  color: #F8972A;
}

.profile-description {
  font-size: 15px;
  color: #333;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.profile-list {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0;
}

.profile-list-item {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
  color: #444;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .profile-container {
    gap: 70px;
  }

  .profile-shape {
    width: 400px;
    height: 280px;
  }

  .profile-row-odd .profile-card {
    margin-left: 500px;
  }

  .profile-row-even .profile-card {
    margin-right: 400px;
  }
}

@media (max-width: 992px) {
  .profile-container {
    gap: 60px;
  }

  .profile-shape {
    width: 400px;
    height: 280px;
  }

  .profile-card {
    width: 65%;
  }

  .profile-row-odd .profile-card {
    margin-left: 430px;
  }

  .profile-row-even .profile-card {
    margin-right: 430px;
  }

  .profile-card-content {
    padding: 20px 25px;
  }
}

@media (max-width: 768px) {
  .profile-section {
    padding: 50px 15px;
  }

  .profile-container {
    gap: 50px;
  }

  .profile-shape {
    width: 400px;
    height: 280px;
  }

  .profile-card {
    width: 70%;
  }

  .profile-row-odd .profile-card {
    margin-left: 400px;
  }

  .profile-row-even .profile-card {
    margin-right: 400px;
  }

  .profile-title {
    font-size: 20px;
  }

  .profile-description {
    font-size: 14px;
  }

  .profile-list-item {
    font-size: 14px;
    margin-bottom: 5px;
  }
}

/* Mobile layout - match the image exactly */
@media (max-width: 576px) {
  .profile-section {
    padding: 40px 15px;
  }

  .profile-container {
    gap: 0px;
  }

  /* Adjust layout for mobile */
  .profile-row {
    flex-direction: column;
    min-height: auto;
    margin-bottom: 0;
    align-items: flex-start;
    height: auto;
    position: relative;
  }

  .profile-row-odd, .profile-row-even {
    justify-content: flex-start;
  }

  /* Shape styling for mobile */
  .profile-shape {
    position: relative;
    width: 70%;
    height: 200px;
    border-radius: 20px;
    transform: rotate(-5deg);
    margin: 0;
  }

  .profile-row-odd .profile-shape {
    left: 0;
    top: 0;
  }

  .profile-row-even .profile-shape {
    right: auto;
    left: 25%;
    top: 0;
  }

  /* Card styling for mobile */
  .profile-card {
    width: 75%;
    margin:   0 !important;
    position: relative;
    top: -30px;
  }

  .profile-row-odd .profile-card {
    left: 25%;
  }

  .profile-row-even .profile-card {
    left: 0;
  }

  .profile-card-content {
    padding: 85px 10px;
  }

  .profile-title {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .profile-description {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .profile-list {
    padding-left: 16px;
  }

  .profile-list-item {
    font-size: 13px;
    line-height: 1.3;
    margin-bottom: 4px;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  .profile-section {
    padding: 30px 20px;
  }

  .profile-container {
    gap: 30px;
  }

  .profile-shape {
    width: 75%;
    height: 200px;
  }

  .profile-card {
    width: 85%;
    top: -0px;
  }

  .profile-card-content {
    padding: 12px 15px;
  }

  .profile-title {
    font-size: 16px;
  }

  .profile-description {
    font-size: 13px;
  }

  .profile-list-item {
    font-size: 12px;
  }
}
