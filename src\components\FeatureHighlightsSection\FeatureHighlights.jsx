
import './FeatureHighlights.css';

const features = [
  {
    id: 1,
    title: 'Client Management',
    description: 'Build strong relationships with a 360° view of every client.'
  },
  {
    id: 2,
    title: 'Project Management',
    description: 'Plan, assign, and track projects across teams and departments.'
  },
  {
    id: 3,
    title: 'Client Onboarding',
    description: 'Create smooth, automated onboarding workflows.'
  },
  {
    id: 10,
    title: 'Invoices',
    description: 'Send branded invoices, track payments, and automate billing cycles.'
  },
  {
    id: 4,
    title: 'Time Tracking',
    description: 'Log billable hours and analyze productivity with built-in timers.'
  },
  {
    id: 9,
    title: 'Collaborators',
    description: 'CollaboratorsAdd clients, freelancers, and team members securely.'
  },
  {
    id: 5,
    title: 'Proposals',
    description: 'Send interactive proposals with pricing tables and approvals.'
  },
  {
    id: 7,
    title: 'Forms',
    description: 'Collect data seamlessly through custom-built intake forms.'
  },
  {
    id: 8,
    title: 'Agreements',
    description: 'Create and send clear, itemized cost estimates.'
  },
  {
    id: 6,
    title: 'E-Signature',
    description: 'Send contracts and agreements with legally binding e-signatures.'
  },
];

const FeatureHighlights = () => {
  return (
    <section className="feature-section">
      <div className="glow-effect"></div>
      <h2>Feature Highlights</h2>
      <div className="feature-grid">
        {features.map((feature, index) => (
          <div className="feature-card" key={index}>
            <div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
            </div>
            <a href={`featuresdetails/${feature.id}`}>Learn More →</a>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FeatureHighlights;
