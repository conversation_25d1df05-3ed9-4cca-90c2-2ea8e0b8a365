import React from 'react';
import './FlexibleContract.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDollarSign, faDatabase, faUsers } from '@fortawesome/free-solid-svg-icons';

const FlexibleContract = () => {
  return (
    <section className="flexible-contract-section">
      <div className="flexible-contract-container">
        <div className="flexible-contract-header">
          <div className="flexible-contract-title">
            <span className="flexible-contract-highlight">Flexible </span>Contract Models to Fit Your Needs
          </div>
        </div>

        <div className="contract-models">
          <div className="contract-model">
            <div className="contract-icon">
              <FontAwesomeIcon icon={faDollarSign} />
            </div>
            <div className="contract-content">
              <h3 className="contract-title">Fixed Price (FP)</h3>
              <p className="contract-description">
                Ideal for projects with a well-defined scope and clear requirements. This
                model provides a set budget and timeline but doesn't allow for changes
                once work begins.
              </p>
            </div>
          </div>

          <div className="contract-model">
            <div className="contract-icon">
              <FontAwesomeIcon icon={faDatabase} />
            </div>
            <div className="contract-content">
              <h3 className="contract-title">Time and Material (T&M)</h3>
              <p className="contract-description">
                Best for agile projects where requirements may evolve over time. This
                model allows flexibility as the project progresses.
              </p>
            </div>
          </div>

          <div className="contract-model">
            <div className="contract-icon">
              <FontAwesomeIcon icon={faUsers} />
            </div>
            <div className="contract-content">
              <h3 className="contract-title">Dedicated Development Team (DDT)</h3>
              <p className="contract-description">
                Perfect for long-term projects requiring continuous development
                and maintenance, giving you a reliable team focused on your success.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FlexibleContract;