/* Let's Build Success Together Section */
.lets-build-section {
  padding: 80px 20px;
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lets-build-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.lets-build-header {
  text-align: left;
  margin-bottom: 60px;
  max-width: 800px;
}

.lets-build-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: #007474;
  margin: 0 0 20px 0;
}

.lets-build-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 500;
}

/* Partnership Models Container - Desktop Grid */
.partnership-models {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  max-width: 1260px;
}

/* Individual Partnership Model */
.partnership-model {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 30px;
  background-color: #f1f1ec;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.partnership-models .partnership-model:nth-child(2),
.partnership-models .partnership-model:nth-child(3) {
  background-color: #ffffff;
}

.partnership-model:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.partnership-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background-color: #007474;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-top: 5px;
}

.partnership-content {
  flex: 1;
}

.partnership-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffab6a;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.partnership-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 500;
}

/* Desktop Responsive Design */
@media (min-width: 768px) {
  .lets-build-section {
    padding: 100px 40px;
  }

  .lets-build-title {
    font-size: 4rem;
  }

  .lets-build-subtitle {
    font-size: 1.2rem;
  }

  .partnership-models {
    gap: 40px;
  }

  .partnership-model {
    padding: 35px;
    gap: 25px;
  }

  .partnership-icon {
    width: 70px;
    height: 70px;
    font-size: 28px;
  }

  .partnership-title {
    font-size: 1.5rem;
  }

  .partnership-description {
    font-size: 1.1rem;
  }
}

/* Large Desktop */
@media (min-width: 1200px) {
  .lets-build-section {
    padding: 120px 60px;
  }

  .lets-build-title {
    font-size: 2.5rem;
    
  }

  .lets-build-subtitle {
    font-size: 1.3rem;
    white-space: nowrap;
  }

  .partnership-models {
    gap: 50px;
  }

  .partnership-model {
    padding: 40px;
    gap: 30px;
  }

  .partnership-icon {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }

  .partnership-title {
    font-size: 1.6rem;
    font-weight: bold;
  }

  .partnership-description {
    font-size: 1.2rem;
  }
}

/* Mobile Responsive Design */
@media (max-width: 767px) {
  .lets-build-section {
    padding: 60px 15px;
    min-height: auto;
  }

  .lets-build-header {
    margin-bottom: 40px;
    text-align: center;
  }

  .lets-build-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .lets-build-subtitle {
    font-size: 1rem;
    line-height: 1.5;
  }

  .partnership-models {
    display: flex;
    flex-direction: column;
    gap: 25px;
    max-width: 100%;
  }

  .partnership-model {
    padding: 25px;
    gap: 15px;
  }

  .partnership-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-top: 0;
  }

  .partnership-title {
    font-size: 1.3rem;
    margin-bottom: 12px;
  }

  .partnership-description {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .lets-build-section {
    padding: 40px 35px;
  }

  .lets-build-title {
    font-size: 2rem;
    text-align: left;
  }

  .lets-build-subtitle {
    font-size: 20px;
    text-align: left;
  }

  .partnership-model {
    padding: 20px;
    gap: 12px;
  }

  .partnership-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .partnership-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
  }

  .partnership-description {
    font-size: 0.9rem;
  }
}