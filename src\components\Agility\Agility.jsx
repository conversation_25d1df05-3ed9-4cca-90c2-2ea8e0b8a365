import React from 'react';
import './Agility.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faCogs, faEye, faRocket } from '@fortawesome/free-solid-svg-icons';

const Agility = () => {
  const agilitySteps = [
    {
      icon: faSearch,
      title: "We start by understanding your goals and requirements in detail to ensure we're fully aligned.",
      isFirst: true
    },
    {
      icon: faCogs,
      title: "Our agile, step-by-step approach prioritizes the most valuable features first.",
      isFirst: false
    },
    {
      icon: faEye,
      title: "You'll get regular demos, so you can see progress firsthand and provide feedback along the way.",
      isFirst: false
    },
    {
      icon: faRocket,
      title: "Before launch, we conduct thorough testing to ensure everything runs smoothly and have a plan in place for ongoing support.",
      isFirst: false
    }
  ];

  return (
    <section className="agility-section">
      <div className="agility-container">
        <div className="agility-header">
          <h2 className="agility-title">
            Agility for your <span className="highlight-text">Digital</span> Journey
          </h2>
          <p className="agility-description">
            Move faster on your technology roadmap with our expert teams. We help you quickly assemble the right talent,
            streamline development, and take the hassle out of daily management—so you can focus on what truly matters.
          </p>
        </div>

        <div className="agility-steps">
          {agilitySteps.map((step, index) => (
            <div key={index} className={`agility-step ${step.isFirst ? 'first-step' : ''}`}>
              <div className="step-icon">
                <FontAwesomeIcon icon={step.icon} />
              </div>
              <div className="step-content">
                <p className="step-description">{step.title}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="curved-background"></div>
    </section>
  );
};

export default Agility;