/* Business and Software Development Services Section */
.business-software-section {
  padding: 80px 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.business-software-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.business-software-header {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.business-software-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: #333333;
  margin: 0 0 20px 0;
}

.business-software-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666666;
  margin: 0;
  font-weight: 400;
}

/* Services Grid - Desktop 3x2 Layout */
.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1100px;
  margin: 0 auto;
}

/* Individual Service Card */
.service-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 30px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.service-icon {
  width: 80px;
  height: 80px;
  background-color: #007474;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  margin-bottom: 25px;
  flex-shrink: 0;
}

.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333333;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.service-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #666666;
  margin: 0;
  font-weight: 400;
  flex: 1;
}

/* Desktop Responsive Design */
@media (min-width: 768px) {
  .business-software-section {
    padding: 100px 40px;
  }

  .business-software-title {
    font-size: 4rem;
  }

  .business-software-subtitle {
    font-size: 1.2rem;
  }

  .services-grid {
    gap: 35px;
  }

  .service-card {
    padding: 45px 35px;
  }

  .service-icon {
    width: 90px;
    height: 90px;
    font-size: 36px;
    margin-bottom: 30px;
  }

  .service-title {
    font-size: 1.6rem;
  }

  .service-description {
    font-size: 1.1rem;
  }
}

/* Large Desktop */
@media (min-width: 1200px) {
  .business-software-section {
    padding: 120px 60px;
  }

  .business-software-title {
    font-size: 4.5rem;
  }

  .business-software-subtitle {
    font-size: 1.3rem;
  }

  .services-grid {
    gap: 40px;
    max-width: 1200px;
  }

  .service-card {
    padding: 50px 40px;
  }

  .service-icon {
    width: 100px;
    height: 100px;
    font-size: 40px;
    margin-bottom: 35px;
  }

  .service-title {
    font-size: 1.75rem;
  }

  .service-description {
    font-size: 1.2rem;
  }
}