/* Flexible Contract Section */
.flexible-contract-section {
  padding: 80px 20px;
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flexible-contract-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.flexible-contract-header {
  text-align: center;
  margin-bottom: 60px;
}

.flexible-contract-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: #333333;
  margin: 0;
}

.flexible-contract-highlight {
  color: #007474;
  position: relative; 
  display: inline-block;
  margin-right: 8px;
}

.flexible-contract-highlight::after {
  content: "";
  display: block;
  width: 60%; 
  height: 3px; 
  background-color: #ffab6a; 
  margin: 5px auto 0; 
  border-radius: 2px; 
}

/* Contract Models Container */
.contract-models {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 600px;
  margin: 0 auto;
}

/* Individual Contract Model */
.contract-model {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 0;
}

.contract-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background-color: #007474;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-top: 5px;
}

.contract-content {
  flex: 1;
}

.contract-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffab6a;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.contract-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  margin: 0;
  font-weight: 500;
}

/* Desktop Responsive Design */
@media (min-width: 768px) {
  .flexible-contract-section {
    padding: 100px 40px;
  }

  .flexible-contract-title {
    font-size: 4rem;
  }

  .contract-models {
    max-width: 800px;
    gap: 50px;
  }

  .contract-model {
    gap: 30px;
  }

  .contract-icon {
    width: 70px;
    height: 70px;
    font-size: 28px;
  }

  .contract-title {
    font-size: 1.75rem;
  }

  .contract-description {
    font-size: 1.1rem;
  }
}

/* Large Desktop */
@media (min-width: 1200px) {
  .flexible-contract-section {
    padding: 120px 60px;
  }

  .flexible-contract-title {
    font-size: 2.5rem;
  }

  .contract-models {
    max-width: 900px;
    gap: 60px;
  }

  .contract-model {
    gap: 35px;
  }

  .contract-icon {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }

  .contract-title {
    font-size: 2rem;
  }

  .contract-description {
    font-size: 1.2rem;
  }
}

/* Mobile Responsive Design */
@media (max-width: 767px) {
  .flexible-contract-section {
    padding: 60px 15px;
    min-height: auto;
  }

  .flexible-contract-header {
    margin-bottom: 40px;
  }

  .flexible-contract-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .contract-models {
    gap: 30px;
    max-width: 100%;
  }

  .contract-model {
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .contract-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-top: 0;
  }

  .contract-title {
    font-size: 1.3rem;
    margin-bottom: 10px;
  }

  .contract-description {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .flexible-contract-section {
    padding: 40px 10px;
  }

  .flexible-contract-title {
    font-size: 2.5rem;
    text-align: left;
    margin-left: 35px;
  }

  .contract-model {
    padding: 15px;
    gap: 12px;
  }

  .contract-icon {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .contract-title {
    font-size: 1.2rem;
  }

  .contract-description {
    font-size: 0.9rem;
  }
}