
import React from 'react';
import './AllInOneSection.css';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import featuresData from '../../data/featuresData';

const AllInOneSection = () => {
  // Feature card data - using the first 10 features from our data store
  const featureCards = featuresData.slice(0, 10).map(feature => ({
    id: feature.id,
    title: feature.title,
    description: feature.shortDescription.split('.')[0] + '.',
    details: feature.description.split('.')[0] + '.'
  }));

  //display the first 10 feature cards
  const displayedFeatures = featureCards.slice(0, 10);

  return (
    <section className="aio-section">
      <div className="aio-container">
        <div className="aio-header">
          <h2 className="aio-title">All-in-One, Yet Built for Focus</h2>
          <p className="aio-description">
            Explore each feature in detail below. Every tool is designed to simplify
            your day-to-day work, automate repetitive tasks, and help you scale.
          </p>
        </div>

        <div className="aio-cards-grid">
          {displayedFeatures.map((feature) => (
            <div className="aio-card" key={feature.id}>
              <h3 className="aio-card-title">{feature.title}</h3>
              <div className="aio-card-content">
                <p className="aio-card-description">{feature.description}</p>
                <p className="aio-card-details">{feature.details}</p>
              </div>
              <Link to={`/featuresdetails/${feature.id}`} className="aio-learn-more">
                Learn More <FontAwesomeIcon icon={faArrowRight} className="aio-arrow-icon" />
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AllInOneSection;
