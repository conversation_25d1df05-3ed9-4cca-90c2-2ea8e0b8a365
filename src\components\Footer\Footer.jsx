import './Footer.css';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebookF,
  faTwitter,
  faInstagram,
  faLinkedinIn
} from "@fortawesome/free-brands-svg-icons";
import footerBg from '../../assets/Footer2.jpg';
import cmsLogo from '../../assets/cms.svg';

const Footer = () => {
  return (
    <footer className="footer" style={{ backgroundImage: `url(${footerBg})` }}>
      <div className="footer-overlay"></div>
      <div className="footer-container">
        <div className="footer-top">
          <div className="footer-column">
            <div className="footer-logo">
              <img src={cmsLogo} alt="Celaeno CMS Logo" className="cms-logo" />
            </div>
            <p className="footer-description">
              <span className="quote-mark opening">“</span>
              Business management platform for modern teams. Simplify your workflows and grow your business.
              <span className="quote-mark closing">”</span>
            </p>
            <div className="social-icons">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                <FontAwesomeIcon icon={faFacebookF} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                <FontAwesomeIcon icon={faTwitter} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                <FontAwesomeIcon icon={faInstagram} />
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
                <FontAwesomeIcon icon={faLinkedinIn} />
              </a>
            </div>
          </div>

          <div className="footer-column">
            <h3 className="footer-title">Product</h3>
            <ul className="footer-links">
              <li><Link to="/pricing">Pricing</Link></li>
              <li><Link to="/industry">Industry</Link></li>
             {/* <li><Link to="/reviews">Reviews</Link></li> */}
            </ul>
          </div>

          <div className="footer-column">
            <h3 className="footer-title">Features</h3>
            <ul className="footer-links">
              <li><Link to="featuresdetails/1">Client Management</Link></li>
              <li><Link to="featuresdetails/2">Project Management</Link></li>
              <li><Link to="featuresdetails/3">Client Onboarding</Link></li>    
              <li><Link to="featuresdetails/4">Time Tracking</Link></li>           
              <li><Link to="featuresdetails/5">Proposals</Link></li>
              <li><Link to="featuresdetails/6">E-signatures</Link></li>
              <li><Link to="featuresdetails/7">Forms</Link></li>
              <li><Link to="featuresdetails/8">Agreements</Link></li>
              <li><Link to="featuresdetails/9">Collaborators</Link></li>
              <li><Link to="featuresdetails/10">Invoices</Link></li>

            </ul>
          </div>

          <div className="footer-column">
            <h3 className="footer-title">Solution</h3>
            <ul className="footer-links">
              <li><Link to="/solution#freelancers">Freelancers</Link></li>
              <li><Link to="/solution#small-business">Small Businesses</Link></li>
              <li><Link to="/solution#medium-business">Medium Businesses</Link></li>
              <li><Link to="/solution#large-organizations">Large Organizations</Link></li>
            </ul>
          </div>

          <div className="footer-column">
            <h3 className="footer-title">Resources</h3>
            <ul className="footer-links">
              <a target='_blank' href="https://helpdesk-new.celaenotechnology.com/knowledgebase.php">Knowledge Base</a><br />
              <a target='_blank' href="https://helpdesk-new.celaenotechnology.com/index.php">Support</a>
              <li><Link to="/Talktosales">Talk to Sales</Link></li>
            </ul>
          </div>
        </div>

        <div className="footer-bottom">
          <p className="copyright">
            &copy; {new Date().getFullYear()} Celaeno. All rights reserved.
          </p>
          <div className="legal-links">
            <Link to="/privacy">Privacy Policy</Link>
            <Link to="/terms">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;