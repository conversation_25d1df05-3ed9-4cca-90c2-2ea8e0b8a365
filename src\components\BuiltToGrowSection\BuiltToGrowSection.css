
.celaeno-growth-section {
  width: 100%;
  background-color: #faf5f0;
  padding: 60px 0;
  overflow: hidden;
  position: relative;
}

/* Bubble decorations */
.celaeno-bubble {
  position: absolute;
  border-radius: 50%;
  background-color: #eaad77;
  opacity: 0.7;
  z-index: 1;
}

.celaeno-bubble-1 {
  width: 120px;
  height: 120px;
  left: -30px;
  top: 20%;
}

.celaeno-bubble-2 {
  width: 80px;
  height: 80px;
  left: 5%;
  bottom: 10%;
}

.celaeno-bubble-3 {
  width: 150px;
  height: 150px;
  right: -40px;
  top: 10%;
}

.celaeno-bubble-4 {
  width: 100px;
  height: 100px;
  right: 5%;
  bottom: 15%;
}

.celaeno-growth-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.celaeno-growth-title {
  color: #00736c;
  text-align: center;
  font-size: 36px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.celaeno-growth-description {
  text-align: center;
  font-size: 16px;
  color: #333;
  margin: 0 auto 60px;
  max-width: 700px;
}

.celaeno-arc-container {
  position: relative;
  height: 300px;
  margin-bottom: -80px;
}

/* The arc */
.celaeno-arc {
 position: absolute;
  width: 100%;
  height: 2350px; /* Double height to create semi-circle */
  border: 1px solid #d4b978;
  border-bottom: none;
  border-radius: 60% 60% 0 0;
  top: 0;
  left: 0;
}

.celaeno-business-types {
  position: absolute;
  width: 100%;
  height: 30%;
}

.celaeno-business-type {
  position: absolute;
}

.celaeno-business-label {
  color: #f8972a;
  font-weight: 500;
  font-size: 14px;
  position: absolute;
}

.celaeno-business-type-1 .celaeno-business-label {
  top: -25px;
  left: -20%;
  transform: translateX(-50%);
}

.celaeno-business-type-2 .celaeno-business-label {
  top: -10px;
  left: 170%;
  transform: translateX(-50%);
}

.celaeno-icon-wrapper {
  width: 60px;
  height: 60px;
  background-color: #00736c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

/* Position icons on the arc */
.celaeno-business-type-1 {
  left: 31%;
  top: 40%;
}

.celaeno-business-type-2 {
  right: 31%;
  top: 40%;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .celaeno-arc-container {
    height: 250px;
  }

  .celaeno-business-type-1 {
    left: 30%;
  }

  .celaeno-business-type-2 {
    right: 30%;
  }
}

@media (max-width: 768px) {
  .celaeno-growth-title {
    font-size: 32px;
  }

  .celaeno-growth-description {
    font-size: 15px;
    margin-bottom: 50px;
  }

  .celaeno-arc-container {
    height: 200px;
  }

  .celaeno-icon-wrapper {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .celaeno-business-type-1 {
    left: 30%;
  }

  .celaeno-business-type-2 {
    right: 30%;
  }
}

@media (max-width: 576px) {
  .celaeno-growth-title {
    font-size: 28px;
  }

  .celaeno-growth-description {
    font-size: 14px;
    margin-bottom: 40px;
  }

  .celaeno-arc-container {
    height: 180px;
  }

  .celaeno-icon-wrapper {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .celaeno-business-type-1 {
    left: 30%;
  }

  .celaeno-business-type-2 {
    right: 30%;
  }

  .celaeno-business-label {
    font-size: 12px;
  }
}
