// Header.jsx
import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Header.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars, faTimes, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import FeaturesDropdown from '../FeaturesDropdown/FeaturesDropdown';
import ResourcesDropdown from '../ResourcesDropdown/ResourcesDropdown';

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [featuresDropdownOpen, setFeaturesDropdownOpen] = useState(false);
  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(false);

  // Get current location to determine active page
  const location = useLocation();
  const { pathname } = location;

  // Handle scroll event to make header sticky
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) { // Adjust this threshold as needed
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Effect to disable/enable scrolling when menu opens/closes
  useEffect(() => {
    if (menuOpen) {
      // Disable scrolling when menu is open
      document.body.style.overflow = 'hidden';
      // Store current scroll position
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.top = `-${window.scrollY}px`;
    } else {
      // Enable scrolling when menu is closed
      const scrollY = document.body.style.top;
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      // Restore scroll position
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0', 10) * -1);
      }
    }

    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
    };
  }, [menuOpen]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
    // Close dropdowns when mobile menu is toggled
    if (!menuOpen) {
      setFeaturesDropdownOpen(false);
      setResourcesDropdownOpen(false);
    }
  };

  // Add a delay before closing the dropdown
  let closeTimeout = null;

  const handleFeaturesMouseEnter = () => {
    if (window.innerWidth > 992) {
      // Clear any existing timeout
      if (closeTimeout) {
        clearTimeout(closeTimeout);
        closeTimeout = null;
      }
      setFeaturesDropdownOpen(true);
    }
  };

  const handleFeaturesMouseLeave = () => {
    if (window.innerWidth > 992) {
      // Set a timeout to close the dropdown after a delay
      closeTimeout = setTimeout(() => {
        setFeaturesDropdownOpen(false);
      }, 500); // 500ms delay before closing
    }
  };

  const handleFeaturesClick = (e) => {
    e.preventDefault();

    // For mobile: toggle dropdown on click
    if (window.innerWidth <= 992) {
      // Only toggle if the mobile menu is open
      if (menuOpen) {
        setFeaturesDropdownOpen(!featuresDropdownOpen);
      }
    } else {
      // For desktop: toggle dropdown on click
      setFeaturesDropdownOpen(!featuresDropdownOpen);
    }
  };

  // Resources dropdown handlers
  const handleResourcesMouseEnter = () => {
    if (window.innerWidth > 992) {
      // Clear any existing timeout
      if (closeTimeout) {
        clearTimeout(closeTimeout);
        closeTimeout = null;
      }
      setResourcesDropdownOpen(true);
    }
  };

  const handleResourcesMouseLeave = () => {
    if (window.innerWidth > 992) {
      // Set a timeout to close the dropdown after a delay
      closeTimeout = setTimeout(() => {
        setResourcesDropdownOpen(false);
      }, 500); // 500ms delay before closing
    }
  };

  const handleResourcesClick = (e) => {
    e.preventDefault();

    // For mobile: toggle dropdown on click
    if (window.innerWidth <= 992) {
      // Only toggle if the mobile menu is open
      if (menuOpen) {
        setResourcesDropdownOpen(!resourcesDropdownOpen);
      }
    } else {
      // For desktop: toggle dropdown on click
      setResourcesDropdownOpen(!resourcesDropdownOpen);
    }
  };

  return (
    <header className={`hero-header ${isSticky ? 'sticky' : ''}`}>
      <Link to="/" className="logo-link">
        <img src="/src/assets/cms.svg" alt="Celaeno Logo" className="logo" />
      </Link>

      <div className={`hamburger-menu ${menuOpen ? 'active' : ''}`} onClick={toggleMenu}>
        <FontAwesomeIcon icon={menuOpen ? faTimes : faBars} className="menu-icon" />
      </div>

      <div className={`nav-container ${menuOpen ? 'active' : ''}`}>
        <div></div>
        <div></div>
        <div className="hero-nav-left">
          <ul className="nav-links">
            <li className={pathname === '/' ? 'active' : ''}>
              <Link to="/">Home</Link>
            </li>
            <li className={pathname === '/solution' ? 'active' : ''}>
              <Link to="/solution">Solution</Link>
            </li>

            <li
              className={`features-link ${pathname.includes('/featuresdetails') ? 'active' : ''}`}
              onMouseEnter={handleFeaturesMouseEnter}
              onMouseLeave={handleFeaturesMouseLeave}
            >
              <a href="#" onClick={handleFeaturesClick}>
                Features <FontAwesomeIcon icon={faChevronDown} className="dropdown-icon" />
              </a>
              <FeaturesDropdown
                isOpen={featuresDropdownOpen}
                onMouseEnter={handleFeaturesMouseEnter}
                onMouseLeave={handleFeaturesMouseLeave}
              />
            </li>
            <li
              className={`resources-link ${pathname === '/Talktosales' ? 'active' : ''}`}
              onMouseEnter={handleResourcesMouseEnter}
              onMouseLeave={handleResourcesMouseLeave}
            >
              <a href="#" onClick={handleResourcesClick}>
                Resources <FontAwesomeIcon icon={faChevronDown} className="dropdown-icon" />
              </a>
              <ResourcesDropdown
                isOpen={resourcesDropdownOpen}
                onMouseEnter={handleResourcesMouseEnter}
                onMouseLeave={handleResourcesMouseLeave}
              />
            </li>
            <li className={pathname === '/pricing' ? 'active' : ''}>
              <Link to="/pricing">Pricing</Link>
            </li>
          </ul>
        </div>

        <div className="hero-nav-right">
          <button className={`contact-btn ${pathname === '/Talktosales' ? 'active' : ''}`}>
            <Link to="/Talktosales">TALK TO SALES</Link>
          </button>
          <a target='_blank' href="https://cms.celaenotechnology.com/login.php" className="login-btn">Login</a>
          <a target='_blank' href="https://cms.celaenotechnology.com/register.php" className="signup-btn">Sign up</a>
        </div>
      </div>
    </header>
  );
};

export default Header;
