
.feature-section {
  width: 100%;
  background-color: #00736c;
  color: white;
  padding: 50px 20px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* White glow effect */
.glow-effect {
  position: absolute;
  top: -20%;
  right: -50%;
  width: 85%;
  height: 150%;
  background: radial-gradient(ellipse at center, rgba(243, 241, 241, 0.637) 0%, rgba(255, 255, 255, 0.05) 40%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
  z-index: 1;
  transform: rotate(-10deg);
}

/* Heading */
.feature-section h2 {
  text-align: center;
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

/* 3x3 grid layout */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  box-sizing: border-box;
  position: relative;
  z-index: 2;
}

/* Each card */
.feature-card {
  background-color: rgba(0, 89, 85, 0.5); /* Darker teal with transparency */
  border-radius: 8px;
  padding: 20px;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid transparent;
}

/* Hover effect for card */
.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 89, 85, 0.7); /* Slightly darker on hover */
  border-color: rgba(255, 255, 255, 0.2);
}

/* Shine effect on hover */
.feature-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -60%;
  width: 20%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(30deg);
  transition: all 0.6s ease;
}

.feature-card:hover::after {
  left: 120%;
}

/* Title inside card */
.feature-card h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
  transition: all 0.3s ease;
}

.feature-card:hover h3 {
  transform: scale(1.03);
  color: #ffffff;
}

/* Description */
.feature-card p {
  font-size: 14px;
  color: #ffffff;
  line-height: 1.4;
  margin: 0;
  flex-grow: 1;
  transition: all 0.3s ease;
}

.feature-card:hover p {
  color: #ffffff;
}

/* Link */
.feature-card a {
  font-size: 12px;
  color: #ffffff;
  text-align: right;
  text-decoration: none;
  margin-top: 15px;
  display: block;
  transition: all 0.3s ease;
  position: relative;
}

/*.feature-card a::after {
  content: '→';
  position: absolute;
  right: 0;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(-5px);
}*/

.feature-card:hover a {
  color: #ffffff;
  font-weight: bold;
  padding-right: 15px;
}

/*.feature-card:hover a::after {
  opacity: 1;
  transform: translateX(5px);
}*/

/* Responsive design for tablets */
@media (max-width: 992px) {
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .feature-card {
    height: auto;
    min-height: 160px;
  }
}

/* Responsive design for mobile */
@media (max-width: 576px) {
  .feature-section {
    padding: 40px 15px;
  }

  .feature-section h2 {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    min-height: 140px;
  }
}