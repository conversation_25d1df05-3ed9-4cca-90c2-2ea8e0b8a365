:root {
  --color-primary: #ffab6a;
  --transition: 0.3s ease;
}

.resources-dropdown {
  position: absolute;
  width: 300px;
  top: calc(100% + 25px);
  border-radius: 20px;
  padding: 20px;
  background-color: #042e33;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: var(--transition);
  height: auto;
  z-index: 1000;
  margin-top: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.resources-dropdown.open {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.resources-dropdown-container {
  position: relative;
}

/* Create a bridge element to make it easier to move the mouse to the dropdown */
.resources-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 35px;
  bottom: -35px;
  left: 0;
  z-index: 999;
  background-color: transparent;
}

.resources-dropdown-container::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #042e33;
}

.resources-dropdown-content {
  width: 100%;
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.resources-list li {
  display: flex;
  cursor: pointer;
  position: relative;
  margin-bottom: 0;
}

.resource-link {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 16px;
  border-radius: 12px;
  transition: var(--transition);
  color: #ffffff;
  text-decoration: none;
  font-size: 15px;
}



.resource-link span {
  text-align: left;
  color: #ffffff;
  flex: 1;
}

.resource-link:hover {
  background-color: rgba(255, 255, 255, 0.04);
  color: var(--color-primary);
}

.resource-link:hover span {
  color: var(--color-primary);
}

.arrow-icon {
  margin-left: 0;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.resource-link:hover .arrow-icon {
  opacity: 1;
  visibility: visible;
  color: var(--color-primary);
}

.resource-icon {
  display: flex;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(0, 116, 116, 0.1);
  color: #007474;
  min-width: 40px;
  justify-content: center;
  align-items: center;
}

/* Different colored icons for each resource */
.resources-list li:nth-child(1) .resource-icon {
  color: #0a99ab;
  background-color: rgba(52, 152, 219, 0.1);
}

.resources-list li:nth-child(2) .resource-icon {
  color: #d77539;
  background-color: rgba(231, 76, 60, 0.1);
}

.resources-list li:nth-child(3) .resource-icon {
  color: #9b59b6;
  background-color: rgba(46, 204, 113, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.resources-list li {
  animation: fadeIn 0.5s forwards;
  opacity: 0;
}

.resources-list li:nth-child(1) { animation-delay: 0.05s; }
.resources-list li:nth-child(2) { animation-delay: 0.1s; }
.resources-list li:nth-child(3) { animation-delay: 0.15s; }

/* Responsive styles */
@media (max-width: 1024px) {
  .resources-dropdown {
    position: absolute;
    border-radius: 15px;
    padding: 0;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition);
    margin-top: 0;
    box-shadow: none;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    z-index: -1;
  }

  .resources-dropdown.open {
    max-height: 500px;
    overflow-y: auto;
    padding: 15px 0;
    margin-top: 10px;
    margin-bottom: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    z-index: 1000;
  }

  .resources-dropdown-container::before {
    display: none;
  }

  .resources-dropdown-content {
    padding: 0 15px;
  }

  .resources-list {
    gap: 10px;
  }

  .resource-link {
    padding: 10px 12px;
    font-size: 14px;
  }

  .resource-icon {
    padding: 8px;
    min-width: 36px;
  }
}

@media (max-width: 768px) {
  .resources-dropdown-content {
    padding: 0 10px;
  }

  .resource-link {
    padding: 8px 10px;
    font-size: 13px;
  }

  .resource-icon {
    padding: 6px;
    min-width: 32px;
  }
}