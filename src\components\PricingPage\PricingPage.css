.pricing-section {
  padding: 150px 20px 180px; /* Extra padding at bottom for the wave */
  background-color: #e3e3e3;
  position: relative;
  overflow: hidden;
}

.pricing-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.pricing-heading {
  color: #00736c;
  font-size: 36px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40px;
}

.pricing-toggle {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 60px;
}

.toggle-btn {
  background-color: transparent;
  border: 1px solid #00736c;
  color: #00736c;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background-color: #00736c;
  color: white;
}

.pricing-cards {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Align to top to show different heights */
  gap: 30px;
  flex-wrap: wrap;
}

/* Base card styling */
.pricing-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 30px;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

/* Different card sizes */
.card-small {
  max-width: 320px;
  min-height: 480px;
}

.card-medium {
  max-width: 350px;
  min-height: 520px;
}

.card-large {
  max-width: 370px;
  min-height: 630px;
}

.plan-name {
  color: #00736c;
  font-size: 24px;
  margin-bottom: 15px;
}

.plan-price {
  margin-bottom: 20px;
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: #00736c;
}

.period {
  font-size: 16px;
  color: #666;
}

.plan-description {
  font-size: 16px;
  color: #333;
  margin-bottom: 25px;
  font-weight: 500;
}

.plan-features {
  list-style-type: none;
  padding: 0;
  margin: 0 0 30px 0;
  flex-grow: 1;
}

.plan-features li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #444;
}

.plan-features li:before {
  content: "•";
  color: #00736c;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.select-btn {
  background-color: #00736c;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
  margin-top: auto; /* Push button to bottom */
}

.select-btn:hover {
  background-color: #005b55;
  transform: translateY(-2px);
}

/* SVG Wave at the bottom */
.pricing-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 150%;
  height: 900px;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 1100px) {
  .pricing-cards {
    gap: 20px;
  }
  
  .card-small, .card-medium, .card-large {
    max-width: 300px;
  }
}

@media (max-width: 992px) {
  .pricing-heading {
    font-size: 32px;
  }
  
  .card-small, .card-medium, .card-large {
    min-height: auto; /* Let height be determined by content on tablets */
  }
}

@media (max-width: 768px) {
  .pricing-section {
    padding: 150px 20px 150px;
  }
  
  .pricing-heading {
    font-size: 28px;
    margin-bottom: 30px;
  }
  
  .pricing-cards {
    flex-direction: column;
    align-items: center;
  }
  
  .pricing-card {
    max-width: 450px !important; /* Override all card widths on mobile */
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .pricing-section {
    padding: 150px 15px 130px;
  }
  
  .pricing-heading {
    font-size: 24px;
  }
  
  .pricing-card {
    padding: 20px;
    width: 90%;
  }
  
  .plan-name {
    font-size: 20px;
  }
  
  .price {
    font-size: 30px;
  }
  
  .pricing-wave {
    height: 100px;
  }
}
