
.scalable-section {
  width: 100%;
  background-color: #e9e9e9;
  padding: 60px 20px;
  text-align: center;
  box-sizing: border-box;
}

.scalable-heading {
  color: #00736c;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 40px;
}

.business-cards-container {
  display: flex;
  justify-content: center;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.business-card {
  padding: 20px;
  border-radius: 4px;
  width: 100%;
  max-width: 260px;
  text-align: left;
  color: white;
}

.business-card.teal {
  background-color: #00736c;
}

.business-card.orange {
  background-color: #ffab6a;
}

.business-type {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.business-description {
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.cta-btn {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.get-started-btn{
  margin-top: 30px;
    padding: 15px 40px;
    background: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;  
    transition: var(--transition);
    display: inline-block;  
}

.get-started-btn:hover {
  background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(77, 182, 172, 0.3);
}

.talk-to-sales-btn {
    margin-top: 30px;
    padding: 15px 40px;
    background: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;  
    transition: var(--transition);
    display: inline-block;  
}

.talk-to-sales-btn:hover {
  background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(77, 182, 172, 0.3);
}


/* Responsive styles */
@media (max-width: 992px) {
  .business-cards-container {
    flex-wrap: wrap;
  }
  
  .business-card {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .business-cards-container {
    flex-direction: column;
    align-items: center;
  }
  
  .business-card {
    max-width: 400px;
  }
  
  .scalable-heading {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .scalable-section {
    padding: 40px 15px;
  }
  
  .business-card {
    max-width: 100%;
  }
}
