:root {
  --primary-color: #4DB6AC;
  --secondary-color: #FFab6a;
  --dark-text: #212121;
  --light-text: #666;
  --background: #f8f9fc;
  --card-bg: #ffffff;
  --transition: all 0.3s ease;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --hover-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.solution-page {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, 'sans-serif';
  background: var(--background);
  color: var(--dark-text);
  padding: 40px 0 80px 0;
  overflow-x: hidden;
}

/* Hero Section */
.solution-hero {
  position: relative;
  text-align: center;
  padding: 120px 0 100px 0;
  background: linear-gradient(135deg, #042e33 0%, #00736c 100%);
  color: #fff;
  overflow: hidden;
  margin-top: -3rem;
  margin-bottom: 60px;
}

.solution-hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin:  75px auto 0 auto;
  padding: 0 20px;
}

.solution-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.solution-hero .highlight {
  color: var(--secondary-color);
  position: relative;
  display: inline-block;
}

.solution-hero .highlight::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(255, 158, 87, 0.3);
  z-index: -1;
}

.solution-hero p {
  font-size: 1.2rem;
  margin-top: 20px;
  opacity: 0.9;
}

.hero-cta-button {
  margin-top: 30px;
  padding: 15px 40px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
  display: inline-block;
}

.hero-cta-button:hover {
  background: #fff;
  color: var(--secondary-color);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.wave {
  position: relative;
  display: block;
  width: 100%;
  height: 80px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23f8f9fc'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23f8f9fc'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23f8f9fc'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
}

/* Tabs Section */
.solution-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: -30px auto 60px;
  max-width: 800px;
  padding: 0 20px;
  position: relative;
  z-index: 10;
}

.solution-tab {
  flex: 1;
  background: var(--card-bg);
  border-radius: 12px;
  padding: 15px 20px;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.solution-tab:hover {
  transform: translateY(-5px);
  box-shadow: var(--hover-shadow);
}

.solution-tab.active {
  border-color: var(--primary-color);
  background-color: rgba(77, 182, 172, 0.05);
}

.tab-icon {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.solution-tab span {
  font-weight: 600;
  font-size: 0.9rem;
}

/* Solutions Section */
.solutions-section {
  display: flex;
  flex-direction: column;
  gap: 60px;
  margin: 0 auto;
  max-width: 1200px;
  padding: 0 20px;
}

.solution-card {
  background: var(--card-bg);
  border-radius: 20px;
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  opacity: 0;
  transform: translateY(30px);
}

.solution-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.solution-card:hover {
  box-shadow: var(--hover-shadow);
}

.solution-card.active {
  border: 2px solid var(--primary-color);
}

.solution-header-bar {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(to right, rgba(77, 182, 172, 0.1), rgba(255, 158, 87, 0.1));
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.solution-number {
  font-size: 3rem;
  font-weight: 800;
  opacity: 0.5;
  margin-right: 20px;
  line-height: 1;
}

.solution-size {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.solution-icon {
  font-size: 1.2rem;
}

.solution-content {
  display: flex;
  padding: 0;
  gap: 30px;
}

.solution-left {
  flex: 1;
  padding: 30px;
}

.solution-left h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 20px 0;
  color: var(--dark-text);
  line-height: 1.3;
}

.solution-desc {
  margin: 0 0 30px 0;
  color: var(--light-text);
  font-size: 1.1rem;
  line-height: 1.6;
}

.why-section h4, .features-section h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.why-section h4::after, .features-section h4::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--secondary-color);
}

.solution-list, .features-list {
  margin: 20px 0 0 0;
  padding: 0;
  list-style: none;
}

.solution-list li{
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
  gap: 15px;
  line-height: 1.5;
}

.features-list li {
  margin-bottom: -7px;
  display: flex;
  align-items: flex-start;
  gap: 15px;
  line-height: 1.5;
}

.check-icon {
  margin-top: 3px;
  height: 26px;
}

.solution-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.solution-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.solution-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.solution-card:hover .solution-image img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3));
}

.features-section {
  padding: 30px;
  background-color: rgba(77, 182, 172, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.trial-btn {
  margin-top: 30px;
  padding: 12px 30px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 6px 18px rgba(255,158,87, 0.2);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 10px;
  width: fit-content;
}

.trial-btn:hover {
  background: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(77,182,172, 0.3);
}

.arrow-icon {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.trial-btn:hover .arrow-icon {
  transform: translateX(5px);
}

/* CTA Section */
.solution-cta-section {
  margin: 80px auto 0;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(77, 182, 172, 0.1) 0%, rgba(255, 158, 87, 0.1) 100%);
  border-radius: 20px;
  max-width: 1000px;
}

.solution-cta-section h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--dark-text);
}

.solution-cta-section p {
  font-size: 1.1rem;
  color: var(--light-text);
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  padding: 15px 40px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(255, 158, 87, 0.25);
  transition: var(--transition);
}

.cta-button:hover {
  background: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(77, 182, 172, 0.3);
}

/* Scroll to top button */
.scroll-top-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  z-index: 100;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
}

.scroll-top-button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 1100px) {
  .solution-content {
    flex-direction: column;
  }

  .solution-image {
    height: 250px;
  }
}

@media (max-width: 950px) {
  .solution-hero h1 {
    font-size: 2.5rem;
  }

  .solution-tabs {
    flex-wrap: wrap;
  }

  .solution-tab {
    min-width: 120px;
  }

  .solution-header-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .solution-number {
    font-size: 2.5rem;
    margin-right: 0;
  }

  .solution-cta-section h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .solution-hero {
    padding: 100px 0 80px 0;
  }
.solution-page {
    padding: 60px 0 60px 0;
  }
  .solution-hero h1 {
    font-size: 2rem;
  }

  .solution-hero p {
    font-size: 1rem;
  }

  .hero-cta-button {
    padding: 12px 30px;
    font-size: 1rem;
    margin-top: 25px;
  }

  .solution-tabs {
    margin-top: -20px;
    gap: 10px;
  }

  .solution-tab {
    padding: 10px 15px;
  }

  .tab-icon {
    font-size: 20px;
  }

  .solution-left h3 {
    font-size: 1.5rem;
  }

  .solution-desc {
    font-size: 1rem;
  }

  .why-section h4, .features-section h4 {
    font-size: 1.2rem;
  }

  .solution-list li, .features-list li {
    font-size: 0.95rem;
  }

  .solution-cta-section {
    padding: 40px 20px;
  }

  .solution-cta-section h2 {
    font-size: 1.6rem;
  }

  .solution-cta-section p {
    font-size: 1rem;
  }

  .cta-button {
    padding: 12px 30px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .solution-hero h1 {
    font-size: 1.8rem;
  }
  .solution-page {
    padding: 60px 0 60px 0;
  }

  .solution-tabs {
    flex-direction: column;
    margin-bottom: 40px;
  }

  .solution-tab {
    flex-direction: row;
    justify-content: center;
  }

  .tab-icon {
    margin-bottom: 0;
    margin-right: 10px;
  }

  .solution-left, .features-section {
    padding: 20px 15px;
  }

  .solution-left h3 {
    font-size: 1.3rem;
  }

  .solution-image {
    height: 200px;
  }

  .solution-cta-section h2 {
    font-size: 1.4rem;
  }

  .scroll-top-button {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
    font-size: 1rem;
  }
}
