// ConnectSection.jsx
import React from 'react';
import './ConnectSection.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileInvoice, faUsers, faHome } from '@fortawesome/free-solid-svg-icons';

const ConnectSection = () => {
  return (
    <section className="connect-section">
      <div className="connect-bg-circles">
        <div className="connect-circle connect-circle-1"></div>
        <div className="connect-circle connect-circle-2"></div>
      </div>

      <div className="connect-container">
        <h2 className="connect-title">Connect to Everything Else</h2>
        <p className="connect-description">
          Link projects to time tracking, invoicing, proposals, and more—no silos here.
        </p>

        {/* Arc container with fixed positioned icons */}
        <div className="connect-arc-container">
          {/* The semi-circle arc */}
          <div className="connect-arc"></div>

          {/* Icons positioned on the arc */}
          <div className="connect-icons">
            {/* Track time icon */}
            <div className="connect-icon-wrapper connect-icon-1">
              <FontAwesomeIcon icon={faHome} className="connect-icon" />
              <span className="connect-label">Track time</span>
              <div className="connect-arrow connect-arrow-1"></div>
            </div>

            {/* Assign to project icon */}
            <div className="connect-icon-wrapper connect-icon-2">
              <FontAwesomeIcon icon={faHome} className="connect-icon" />
              <span className="connect-label">Assign to project</span>
              <div className="connect-arrow connect-arrow-2"></div>
            </div>

            {/* Invoice billable hours icon */}
            <div className="connect-icon-wrapper connect-icon-3">
              <FontAwesomeIcon icon={faFileInvoice} className="connect-icon" />
              <span className="connect-label">Invoice billable hours</span>
              <div className="connect-arrow connect-arrow-3"></div>
            </div>

            {/* Share report with client icon */}
            <div className="connect-icon-wrapper connect-icon-4">
              <FontAwesomeIcon icon={faUsers} className="connect-icon" />
              <span className="connect-label">Share report with client</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ConnectSection;
