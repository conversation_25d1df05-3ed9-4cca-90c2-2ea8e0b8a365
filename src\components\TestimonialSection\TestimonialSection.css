
.testimonial-section {
  padding: 80px 20px;
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}


.quote-icon {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: #f08e44;
  font-size: 40px;
  opacity: 0.8;
  z-index: 3; 
}

/* Responsive styles for quote icon */
@media (max-width: 768px) {
  .quote-icon {
    font-size: 35px;
    top: 20px;
  }
}

@media (max-width: 576px) {
  .quote-icon {
    font-size: 30px;
    top: 15px;
  }
}

@media (max-width: 400px) {
  .quote-icon {
    font-size: 25px;
    top: 10px;
    position: relative;
    margin-bottom: 15px;
    display: block;
    text-align: center;
  }
}


.testimonial-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 70px; 
}

.testimonial-card {
  background-color: #fff;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 40px;
  max-width: 700px;
  width: 100%;
  min-height: 300px;
  transition: all 0.3s ease;
}

.testimonial-card h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 30px;
  font-size: 28px;
  color: #333;
}

.testimonial-content {
  display: flex;
  flex-direction: column;
}

.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin-bottom: 30px;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
  border: 2px solid #f8b37e;
}

.author-info h4 {
  margin: 0 0 5px 0;
  font-size: 18px;
  color: #333;
}

.author-info p {
  margin: 0;
  color: #777;
  font-size: 14px;
}

.nav-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f8b37e;
  border: none;
  color: white;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: absolute;
  z-index: 2;
}

.nav-button:hover {
  background-color: #f69d5c;
  transform: scale(1.05);
}

.nav-button.prev {
  left: 0;
}

.nav-button.next {
  right: 0;
}

.testimonial-indicators {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ddd;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background-color: #f8b37e;
  transform: scale(1.2);
}

/* Animation for card transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.testimonial-content {
  animation: fadeIn 0.5s ease;
}

/* Responsive styles */
@media (max-width: 768px) {
  .testimonial-section {
    padding: 60px 20px;
  }
  
  .testimonial-container {
    padding: 0 60px; 
  }
  
  .testimonial-card {
    padding: 30px;
    min-height: auto;
  }
  
  .nav-button {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .testimonial-section {
    padding: 50px 10px;
  }
  
  .testimonial-container {
    padding: 0 45px; 
  }
  
  .testimonial-card {
    padding: 20px;
  }
  
  .testimonial-card h2 {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .testimonial-text {
    font-size: 14px;
  }
  
  .author-avatar {
    width: 45px;
    height: 45px;
  }
  
  .nav-button {
    width: 35px;
    height: 35px;
    top: 50%;
    transform: translateY(-50%);
  }
  
  .nav-button:hover {
    transform: translateY(-50%) scale(1.05);
  }
  
  .nav-button.prev {
    left: 5px;
  }
  
  .nav-button.next {
    right: 5px;
  }
}


@media (max-width: 400px) {
  .testimonial-container {
    padding: 0 40px; 
  }
  
  .testimonial-card {
    padding: 15px;
  }
  
  .testimonial-text {
    margin-bottom: 20px;
  }
  
  .nav-button {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }
  
  .nav-button.prev {
    left: 0;
  }
  
  .nav-button.next {
    right: 0;
  }
  
  .author-avatar {
    width: 40px;
    height: 40px;
  }
}
