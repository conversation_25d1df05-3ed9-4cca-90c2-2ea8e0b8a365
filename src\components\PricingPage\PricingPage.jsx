import React, { useState } from 'react';
import './PricingPage.css';
import Header from '../Header/Header';

const PricingPage = () => {
  const [billingCycle, setBillingCycle] = useState('monthly');

  const handleBillingChange = (cycle) => {
    setBillingCycle(cycle);
  };

  // Price data for different billing cycles
  const pricingData = {
    monthly: {
      starter: {
        price: '$29',
        period: '/month'
      },
      growth: {
        price: '$78',
        period: '/month'
      },
      professional: {
        price: '$149',
        period: '/month'
      }
    },
    yearly: {
      starter: {
        price: '$290',
        period: '/year'
      },
      growth: {
        price: '$780',
        period: '/year'
      },
      professional: {
        price: '$1490',
        period: '/year'
      }
    }
  };

  const currentPricing = pricingData[billingCycle];

  return (
    <section className="pricing-section">
      <Header />
      <div className="pricing-container">
        <h1 className="pricing-heading">Affordable, Scalable, and Tailored to Your Business Needs</h1>
        
        <div className="pricing-toggle">
          <button 
            className={`toggle-btn ${billingCycle === 'monthly' ? 'active' : ''}`}
            onClick={() => handleBillingChange('monthly')}
          >
            Monthly
          </button>
          <button 
            className={`toggle-btn ${billingCycle === 'yearly' ? 'active' : ''}`}
            onClick={() => handleBillingChange('yearly')}
          >
            Yearly
          </button>
        </div>
        
        <div className="pricing-cards">
          {/* Starter Plan - Small Card */}
          <div className="pricing-card card-small">
            <h2 className="plan-name">Starter Plan</h2>
            <div className="plan-price">
              <span className="price">{currentPricing.starter.price}</span>
              <span className="period">{currentPricing.starter.period}</span>
            </div>
            
            <p className="plan-description">Ideal for Small Businesses and Startups</p>
            
            <ul className="plan-features">
              <li>1 User (Additional users at a fee)</li>
              <li>Client Management</li>
              <li>Time Tracking</li>
              <li>Task and Project Management</li>
              <li>Client Onboarding Forms</li>
              <li>Basic Reporting & Analytics</li>
              <li>Email Support</li>
            </ul>
            
            <button className="select-btn">Select</button>
          </div>
          
          {/* Growth Plan - Medium Card */}
          <div className="pricing-card card-medium">
            <h2 className="plan-name">Growth Plan</h2>
            <div className="plan-price">
              <span className="price">{currentPricing.growth.price}</span>
              <span className="period">{currentPricing.growth.period}</span>
            </div>
            
            <p className="plan-description">Designed for Growing Businesses</p>
            
            <ul className="plan-features">
              <li>1 User (Additional users at a fee)</li>
              <li>Client Management</li>
              <li>Time Tracking</li>
              <li>Task and Project Management</li>
              <li>Up to 5 Users</li>
              <li>All features from Starter Plan</li>
              <li>Proposals & Estimations</li>
              <li>Collaborators (Invite team members)</li>
              <li>Integrations (API & Third-party tools)</li>
              <li>Automated Invoicing & Billing</li>
              
            </ul>
            
            <button className="select-btn">Select</button>
          </div>
          
          {/* Professional Plan - Large Card */}
          <div className="pricing-card card-large">
            <h2 className="plan-name">Professional Plan</h2>
            <div className="plan-price">
              <span className="price">{currentPricing.professional.price}</span>
              <span className="period">{currentPricing.professional.period}</span>
            </div>
            
            <p className="plan-description">For Established Businesses and Teams</p>
            
            <ul className="plan-features">
              <li>Up to 15 Users</li>
              <li>All features from Growth Plan</li>
              <li>Unlimited Proposals, Projects, and Estimations</li>
              <li>Advanced Time Tracking & Billing Features</li>
              <li>Client Portal with Custom Branding</li>
              <li>Full Reporting & Analytics Dashboards</li>
              <li>Team Collaboration Tools</li>
              <li>Phone & Chat Support</li>
            </ul>
            
            <button className="select-btn">Select</button>
          </div>
        </div>
      </div>
      
      <svg className="pricing-wave" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" preserveAspectRatio="none">
        <path fill="#00736c" d="M0,96L80,112C160,128,320,160,480,160C640,160,800,128,960,128C1120,128,1280,160,1360,176L1440,192L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"></path>
      </svg>
    </section>
  );
};

export default PricingPage;
