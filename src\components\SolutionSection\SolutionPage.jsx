import React, { useState, useEffect, useRef } from "react";
import "./SolutionPage.css";
import Header from "../Header/Header";
import tick from "../../assets/tick.png";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faArrowRight, faBuilding, faStore, faUser, faCity, faArrowUp } from '@fortawesome/free-solid-svg-icons';

const solutions = [
  {
    size: "Freelancers",
    number: "01",
    title: "Manage Clients and Projects with Ease, All in One Place",
    description:
      "As a freelancer, you juggle multiple clients, deadlines, and projects on your own. Celaeno Client Manager’s Freelancer Plan is designed to simplify your workflow, save time, and keep everything organized—so you can focus more on your craft and less on admin work.",
    why: [
      "Affordable & Tailored for Freelancers: Get professional tools without the overhead cost of large-team software.",
    "All-in-One Dashboard: Manage your clients, tasks, and deadlines from one simple interface.",
    "Easy Invoicing & Payments: Create invoices in minutes, track payments, and ensure you get paid on time.",
    "Portfolio & Proposals: Send polished proposals and showcase your work to win new clients faster.",
    "Stay Organized: Keep notes, files, and project updates together to avoid missing important details.",
  
    ],
    features: [
      "Client Management: Store client contact details, notes, and project history in one place.",
    "Task & Project Tracking: Stay on top of deadlines with a simple task and project management system.",
    "Invoicing & Payment Tracking: Generate professional invoices and keep track of payment status easily.",
    "Proposal Templates: Create and send customized proposals that help you land more clients.",
    "Document & File Sharing: Share files and updates securely with your clients.",
    ],
    cta: "Start Your Free Trial",
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?fit=crop&w=600&q=80",
  },
  {
    size: "Small Business",
    number: "02",
    title: "Simplify Your Workflow, Maximize Your Efficiency",
    description:
      "As a small business, you need solutions that help you stay organized without overwhelming you. Celaeno Client Manager’s Starter Plan is designed with small businesses in mind. It provides essential features at an affordable price, allowing you to focus on what matters most: growing your business.",
    why: [
      "Affordable & Easy to Use: Get up and running quickly with minimal setup and a simple, intuitive interface.",
      "Client Management Made Simple: Keep all your client information in one place, with easy access to contact details, project updates, and tasks.",
      "Time Tracking & Invoicing: Track billable hours, create invoices automatically, and get paid faster.",
      "Custom Forms & Client Onboarding: Customize onboarding forms and streamline your client intake process.",
      "Stay Organized: Manage tasks, projects, and deadlines from a single dashboard to reduce administrative time.",
    ],
    features: [
      "Client Management: Store and track client details, projects, tasks, and more.",
      "Time Tracking: Track the hours you work for each client or project.",
      "Automated Invoicing: Automatically generate invoices based on time tracked or pre-set rates.",
      "Proposals & Estimations: Easily create and send professional proposals for new projects.",
      "Collaboration: Invite clients to collaborate, share documents, and communicate in one place.",
    ],
    cta: "Start Your Free Trial",
    image: "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?fit=crop&w=600&q=80",
  },
  {
    size: "Medium Business",
    number: "03",
    title: "Scale Your Operations with Advanced Tools for Growing Teams",
    description:
      "As your business expands, so do the complexities of managing clients and projects. Celaeno Client Manager’s Growth Plan offers the tools and features you need to collaborate effectively, track progress, and streamline operations—all at a price that scales with your business.",
    why: [
      "Streamlined Collaboration: Manage multiple users, assign tasks, and collaborate with your team in real-time.",
      "Integrations with Your Tools: Connect with other software tools you use through API integrations for seamless workflows.",
      "Advanced Reporting & Analytics: Monitor business performance with in-depth reports and analytics.",
      "Enhanced Client Experience: Provide a custom client portal for easier project updates and communication.",
      "Automation & Workflow Efficiency: Automate recurring tasks, such as invoicing and reminders, to reduce administrative work.",
    ],
    features: [
      "Client & Project Management: Manage clients, projects, tasks, and communications all in one platform.",
      "Team Collaboration: Invite multiple team members and assign specific tasks, ensuring everyone is aligned.",
      "Custom Workflows & Automations: Set up custom workflows that automate day-to-day tasks and processes.",
      "Time Tracking: Track billable hours and convert them into invoices with ease.",
      "Proposals & Invoices: Create and send professional proposals, estimates, and invoices to clients in seconds.",
    ],
    cta: "Start Your Free Trial",
    image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?fit=crop&w=600&q=80",
  },
  {
    size: "Large Organization",
    number: "04",
    title: "Scalable Solutions for Large Teams and Complex Operations",
    description:
      "Large organizations often have complex workflows, multiple teams, and various client projects to manage. Celaeno Client Manager’s Professional Plan provides enterprise-grade tools to support large teams, streamline client management, and boost productivity—while keeping costs manageable.",
    why: [
      "Enterprise-Level Scalability: Manage unlimited users, clients, and projects without worrying about limitations.",
      "Full Customization: Customize client portals, reports, and workflows to fit your unique business needs.",
      "Advanced Analytics & Reporting: Access detailed analytics and reports that provide insights into business performance.",
      "Global Collaboration: With multiple teams and departments, real-time collaboration and task management have never been easier.",
      "24/7 Priority Support: Receive priority support and dedicated account management to ensure your team’s success.",
    ],
    features: [
      "Custom Client Portal: Offer a branded experience for your clients to view project updates, documents, and communicate with your team.",
      "Unlimited User Access: Manage as many team members, clients, and projects as needed with no additional charges.",
      "Integrations & API Access: Easily integrate with your existing tools to create a seamless workflow across your organization.",
      "Advanced Time Tracking: Track billable hours for large teams, and automate the invoicing process based on tracked time.",
      "Comprehensive Reporting: Gain deep insights into performance, project timelines, and team productivity with custom reporting tools.",
    ],
    cta: "Start Your Free Trial",
    image: "https://images.unsplash.com/photo-**********-4c4c79ecde51?fit=crop&w=600&q=80",
  },
];

export default function SolutionPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [isVisible, setIsVisible] = useState(Array(solutions.length).fill(false));
  const [showScrollTop, setShowScrollTop] = useState(false);
  const solutionRefs = useRef(solutions.map(() => React.createRef()));

  // Function to get section index from hash
  const getSectionIndexFromHash = (hash) => {
    const hashMap = {
      '#freelancers': 0,
      '#small-business': 1,
      '#medium-business': 2,
      '#large-organizations': 3
    };
    return hashMap[hash] !== undefined ? hashMap[hash] : 0;
  };

  // Handle hash navigation on component mount and hash change
  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (hash) {
        const sectionIndex = getSectionIndexFromHash(hash);
        setActiveTab(sectionIndex);

        // Scroll to the section after a short delay to ensure the component is rendered
        setTimeout(() => {
          const element = solutionRefs.current[sectionIndex].current;
          if (element) {
            const yOffset = -100;
            const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
            window.scrollTo({
              top: y,
              behavior: 'smooth'
            });
          }
        }, 100);
      }
    };

    // Handle initial hash navigation
    handleHashNavigation();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashNavigation);

    return () => {
      window.removeEventListener('hashchange', handleHashNavigation);
    };
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index'));
            setIsVisible((prev) => {
              const newState = [...prev];
              newState[index] = true;
              return newState;
            });
          }
        });
      },
      { threshold: 0.2 }
    );

    const elements = document.querySelectorAll('.solution-card');
    elements.forEach((el) => observer.observe(el));

    // Add scroll event listener to show/hide scroll-to-top button
    const handleScroll = () => {
      if (window.pageYOffset > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      elements.forEach((el) => observer.unobserve(el));
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const getBusinessIcon = (size) => {
    if (size === "Freelancers") return faUser;
    if (size === "Small Business") return faStore;
    if (size === "Medium Business") return faBuilding;
    return faCity;
  };

  const handleTabClick = (index) => {
    setActiveTab(index);

    // Update URL hash based on the selected section
    const hashMap = ['#freelancers', '#small-business', '#medium-business', '#large-organizations'];
    const newHash = hashMap[index];
    if (newHash) {
      window.history.pushState(null, null, newHash);
    }

    // Scroll to the corresponding section with smooth behavior
    const element = solutionRefs.current[index].current;
    if (element) {
      const yOffset = -100; // Offset to account for fixed header
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

      window.scrollTo({
        top: y,
        behavior: 'smooth'
      });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="solution-page">
      <Header />

      <div className="solution-hero">
        <div className="solution-hero-content">
          <h1>Don't wait! Start your <span className="highlight">14-day free trial</span> today</h1>
          <p>Experience the full power of Celaeno Client Manager for your business.</p>
          <button className="hero-cta-button">Start Free Trial</button>
        </div>
      </div>

      <div className="solution-tabs">
        {solutions.map((sol, idx) => (
          <div
              key={idx}
              className={`solution-tab ${activeTab === idx ? 'active' : ''}`}
              onClick={() => handleTabClick(idx)}
          >
            <FontAwesomeIcon icon={getBusinessIcon(sol.size)} className="tab-icon" />
            <span>{sol.size}</span>
          </div>
        ))}
      </div>

      <section className="solutions-section">
        {solutions.map((sol, idx) => {
          // Create ID based on solution size
          const sectionId = sol.size.toLowerCase().replace(/\s+/g, '-');

          return (
            <div
              className={`solution-card ${isVisible[idx] ? 'visible' : ''} ${activeTab === idx ? 'active' : ''}`}
              key={idx}
              id={sectionId}
              data-index={idx}
              ref={solutionRefs.current[idx]}
            >
            <div className="solution-header-bar">
              <div className="solution-number" style={{ color: idx % 2 === 0 ? "#4DB6AC" : "#FF9E57" }}>
                {sol.number}
              </div>
              <h2 className="solution-size">
                <FontAwesomeIcon icon={getBusinessIcon(sol.size)} className="solution-icon" />
                {sol.size}
              </h2>
            </div>

            <div className="solution-content">
              <div className="solution-left">
                <h3>{sol.title}</h3>
                <p className="solution-desc">{sol.description}</p>
                <div className="why-section">
                  <h4>Why Choose This Solution</h4>
                  <ul className="solution-list">
                    {sol.why.map((w, i) => (
                      <li key={i}>
                        <img src={tick} alt="tick" className="check-icon" />
                        <span>{w}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="solution-right">
                <div className="solution-image">
                  <img src={sol.image} alt={sol.size} />
                  <div className="image-overlay"></div>
                </div>

                <div className="features-section">
                  <h4>Key Features</h4>
                  <ul className="features-list">
                    {sol.features.map((f, i) => (
                      <li key={i}>
                        <img src={tick} alt="tick" className="check-icon" />
                        <span>{f}</span>
                      </li>
                    ))}
                  </ul>
                  <button className="trial-btn">
                    {sol.cta} <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          );
        })}
      </section>

      {/* <div className="solution-cta-section">
        <h2>Ready to transform your client management?</h2>
        <p>Start your free 14-day trial today. No credit card required.</p>
        <button className="cta-button">Get Started Now</button>
      </div> */}

      {showScrollTop && (
        <button className="scroll-top-button" onClick={scrollToTop}>
          <FontAwesomeIcon icon={faArrowUp} />
        </button>
      )}
    </div>
  );
}
