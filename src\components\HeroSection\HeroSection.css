.hero {
  background: linear-gradient(135deg, #fef9f4, #e3e3e3);
  padding: 40px 20px 0px;
  border-radius: 20px;
  max-width: none;
  margin: auto;
}
.hero-wrapper{
/* margin-top: 20px;
  padding: 0 30px;*/
}
.gradient-text {
  background: linear-gradient(to right, #007474 31%, #50A6A5 67%);
  background-clip: text;
  -webkit-background-clip: text;

  /* Makes the text transparent so the gradient shows */
  color: transparent;
  -webkit-text-fill-color: transparent;
}

.hero-content {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
}

.hero-content h1 {
  margin-top: 140px;
  font-size: 2.8rem;
  font-weight: 700;
  color: #004d4d;
  margin-bottom: 30px;
  line-height: 1.3;
}

.hero-content .highlight {
  color: #269999;
}

.hero-content p {
  font-size: 1.1rem;
  color: #333;
  max-width: 750px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 500;
  margin-bottom: 20px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-nav-right {
    margin-top: 15px;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 1.6rem;
  }

  .nav-links {
    flex-direction: column;
    gap: 10px;
  }

  .hero-nav-right {
    flex-direction: column;
    width: 100%;
  }

  .hero-nav-right button {
    width: 100%;
  }
}

.lets-go-btn-home {
  margin-top: 30px;
  margin-bottom: 10px;
  padding: 15px 40px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
  display: inline-block;
}

.lets-go-btn-home:hover {
  background: #fff;
  color: var(--secondary-color);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

.lets-go-btn {
  margin-top: 30px;
  padding: 15px 40px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
  display: inline-block;
}

/* .lets-go-btn:before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
} */

.lets-go-btn:hover {
  background: #fff;
  color: var(--secondary-color);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

.cta-container {
  margin-top: -2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.free-trial-text {
  margin-top: 30px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.lets-go-btn:hover:before {
  left: 100%;
}

.lets-go-btn:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px rgba(0, 115, 108, 0.3);
}

.arrow-icon {
  width: 18px;
  height: 18px;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.lets-go-btn:hover .arrow-icon {
  transform: translateX(4px);
}

