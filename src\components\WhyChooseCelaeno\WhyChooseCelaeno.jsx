
import './WhyChooseCelaeno.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCube, 
  faUsers, 
  faFileContract, 
  faSlidersH, 
  faMoneyBillWave 
} from '@fortawesome/free-solid-svg-icons';

const features = [
  {
    text: "Centralized Client & Project Hub",
    icon: faCube
  },
  {
    text: "Tailored for agencies, consultants, and service-based businesses",
    icon: faUsers
  },
  {
    text: "Automate onboarding, billing, proposals, and agreements",
    icon: faFileContract
  },
  {
    text: "Fully customizable workflows",
    icon: faSlidersH
  },
  {
    text: "Competitive pricing with zero learning curve",
    icon: faMoneyBillWave
  }
];

const WhyChooseCelaeno = () => {
  return (
    <section className="why-wrapper">
      <h2 className="why-heading">Why Choose Celaeno Client Manager?</h2>
      <div className="why-cards">
        {features.map((feature, index) => (
          <div key={index} className="why-card">
            <div className="icon-container">
              <FontAwesomeIcon icon={feature.icon} className="feature-icon" />
            </div>
            <p className="feature-text">{feature.text}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default WhyChooseCelaeno;
