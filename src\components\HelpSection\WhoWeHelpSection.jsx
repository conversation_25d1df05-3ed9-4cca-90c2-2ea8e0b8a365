// ConnectSection.jsx
import React from 'react';
import './WhoWeHelpSection.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileInvoice, faUsers, faHome } from '@fortawesome/free-solid-svg-icons';

const WhoWeHelpSection = () => {
  return (
    <section className="connect-section">
      {/* <div className="connect-bg-circles">
        <div className="connect-circle connect-circle-1"></div>
        <div className="connect-circle connect-circle-2"></div>
      </div> */}
            {/* Bubble decorations */}
      <div className="bubble bubble-1"></div>
      <div className="bubble bubble-2"></div>
      <div className="bubble bubble-3"></div>
      <div className="bubble bubble-4"></div>

      <div className="connect-container">
        <h2 className="connect-title">Who We Help</h2>
        {/* <p className="connect-description">
          Link projects to time tracking, invoicing, proposals, and more—no silos here.
        </p> */}

        {/* Arc container with fixed positioned icons */}
        <div className="connect-arc-container">
          {/* The semi-circle arc */}
          <div className="connect-arc"></div>

          {/* Icons positioned on the arc */}
          <div className="connect-icons">
            {/* Track time icon */}
            <div className="connect-icon-wrapper connect-icon-1">
              <FontAwesomeIcon icon={faHome} className="connect-icon" />
              <span className="connect-label">Freelancer</span>
              {/* <div className="connect-arrow connect-arrow-1"></div> */}
            </div>

            {/* Assign to project icon */}
            <div className="connect-icon-wrapper connect-icon-2">
              <FontAwesomeIcon icon={faHome} className="connect-icon" />
              <span className="connect-label">Consulting Forms</span>
              {/* <div className="connect-arrow connect-arrow-2"></div> */}
            </div>

            {/* Invoice billable hours icon */}
            <div className="connect-icon-wrapper connect-icon-3">
              <FontAwesomeIcon icon={faFileInvoice} className="connect-icon" />
              <span className="connect-label">Management Forms</span>
              {/* <div className="connect-arrow connect-arrow-3"></div> */}
            </div>

            {/* Share report with client icon */}
            <div className="connect-icon-wrapper connect-icon-4">
              <FontAwesomeIcon icon={faUsers} className="connect-icon" />
              <span className="connect-label">Architecture Studio</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoWeHelpSection;
