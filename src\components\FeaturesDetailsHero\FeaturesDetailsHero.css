
.celaeno-hero-section {
  width: 100%;
  background: linear-gradient(to bottom, #fff8f0, #fff0e0);
  padding: 60px 50px;
  text-align: center;
  box-sizing: border-box;
}

.celaeno-hero-container {
  max-width: 1000px;
  margin: 0 auto;
}

.celaeno-hero-title {
  color: #f8972a;
  font-size: 44px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 30px 0;
  margin-top: 70px;
}

.celaeno-hero-title-line {
  display: block;
}

.celaeno-hero-description {
  font-size: 18px;
  color: #333;
  max-width: 800px;
  margin: 0 auto 50px;
  line-height: 1.6;
}

.celaeno-hero-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #00736c;
}

.celaeno-hero-tag {
  font-weight: 500;
}

.celaeno-hero-divider {
  color: #00736c;
  margin: 0 4px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .celaeno-hero-section {
    padding: 80px 20px;
  }

  .celaeno-hero-title {
    font-size: 36px;
  }

  .celaeno-hero-description {
    font-size: 16px;
    margin-bottom: 40px;
  }

  .celaeno-hero-tags {
    font-size: 13px;
    gap: 6px;
  }
}

.cta-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.free-trial-text {
  margin-top: 10px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

@media (max-width: 576px) {
  .celaeno-hero-section {
    padding: 80px 15px;
  }

  .celaeno-hero-title {
    font-size: 30px;
  }

  .celaeno-hero-description {
    font-size: 15px;
    margin-bottom: 30px;
  }

  .celaeno-hero-tags {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .celaeno-hero-divider {
    display: none;
  }

  .free-trial-text {
    font-size: 13px;
  }
}
