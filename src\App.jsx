
import { Route, Routes } from "react-router-dom";
import Home from "./pages/Home";
import Pricing from "./pages/Pricing/Pricing";
import Featuresdetails from "./pages/Featuresdetails/Featuresdetails";
import Footer from "./components/Footer/Footer";
import Talktosalespage from "./pages/Talktosales/Talktosalespage";
import Header from "./components/Header/Header";
import Solution from "./pages/Solution/Solution";


const App = () => {
  return (
    <div className="app-container">
      {/* <Header /> */}
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/Featuresdetails/:featureId" element={<Featuresdetails />} />
        <Route path="/Pricing" element={<Pricing />} />
        <Route path="/Talktosales" element={<Talktosalespage />} />
        <Route path="/Solution" element={<Solution />} />
      </Routes>
      <Footer/>
       </div>
  );
};

export default App;
