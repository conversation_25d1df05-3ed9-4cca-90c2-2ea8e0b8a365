.sales-section {
  position: relative;
  background: #faf5f0;
  padding: 0 0 0 0;
  min-height: 1cqh;
  overflow: hidden;
}

/* .sales-wave-top,
.sales-wave-bottom {
  width: 100%;
  height: 80px;
  position: absolute;
  left: 0;
  z-index: 1;
} */

.sales-wave-top {
  top: 0;
}

.sales-wave-bottom {
  bottom: 0;
}

.sales-wave-top svg {
  width: 100%;
  height: 100%;
  display: none;
}

.sales-wave-bottom svg {
  width: 100%;
  height: 100%;
  display: none;
}

.sales-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 100px 20px 60px 20px;
  display: flex;
  gap: 40px;
  position: relative;
  z-index: 2;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.7s cubic-bezier(.4,2,.6,1), transform 0.7s cubic-bezier(.4,2,.6,1);
}

.sales-section.in-view .sales-container {
  opacity: 1;
  transform: translateY(0);
}

.sales-content {
  flex: 3;
  background: #fff;
  padding: 40px 32px;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.13);
  margin-bottom: 40px;
  animation: fadeInUp 1s cubic-bezier(.4,2,.6,1) 0.2s both;
}

.sales-aside {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 32px;
  animation: fadeInUp 1s cubic-bezier(.4,2,.6,1) 0.35s both;
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(60px);}
  to { opacity: 1; transform: translateY(0);}
}

.sales-title {
  color: #00736c;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.sales-desc {
  font-size: 1.12rem;
  margin-bottom: 32px;
  color: #444;
}

.sales-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
  animation: fadeInUp 1s cubic-bezier(.4,2,.6,1) 0.4s both;
}

.form-row {
  display: flex;
  gap: 18px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.sales-form label {
  font-weight: 500;
  font-size: 0.97rem;
  color: #00736c;
}

.sales-form input,
.sales-form select,
.sales-form textarea {
  border: 1.5px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 1rem;
  margin-top: 2px;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #f9f9f9;
  resize: none;
  box-shadow: 0 1px 4px rgba(0,0,0,0.02);
}

.sales-form input:focus,
.sales-form select:focus,
.sales-form textarea:focus {
  border-color: #00736c;
  background: #fff;
  box-shadow: 0 2px 8px #00736c22;
}

.sales-submit {
  background: linear-gradient(90deg,#00736c,#009688 80%);
  color: #fff;
  border: none;
  padding: 15px 0;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 9px;
  transition: background 0.25s, box-shadow 0.25s, transform 0.2s;
  box-shadow: 0 2px 10px #00736c22;
  position: relative;
  overflow: hidden;
}

.sales-submit:hover {
  background: linear-gradient(90deg,#f8972a,#fdc488 90%);
  color: #fff;
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 4px 16px #f8972a35;
}

.btn-arrow {
  transition: transform 0.3s cubic-bezier(.5,2,.6,1);
}
.sales-submit:hover .btn-arrow {
  transform: translateX(5px) scale(1.13) rotate(4deg);
}

.sales-aside {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.sales-card,
.sales-contact-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(0,0,0,0.10);
  padding: 28px 22px;
  opacity: 0;
  transform: translateY(40px);
  animation: fadeInUp 0.9s cubic-bezier(.4,2,.6,1) 0.5s both;
}

.sales-section.in-view .sales-card,
.sales-section.in-view .sales-contact-card {
  opacity: 1;
  transform: translateY(0);
}

.sales-card h2 {
  color: #00736c;
  font-size: 1.23rem;
  margin-bottom: 14px;
}

.sales-card ul {
  margin: 0;
  padding-left: 18px;
  color: #333;
  font-size: 1.08rem;
}

.sales-card li {
  margin-bottom: 8px;
  position: relative;
  line-height: 1.6;
}

.sales-card li:before {
  content: '';
  position: absolute;
  left: -15px;
  top: 10px;
  width: 7px;
  height: 7px;
  background: #f8972a;
  border-radius: 50%;
}

.sales-contact-card h3 {
  font-size: 1.08rem;
  color: #f8972a;
  margin-bottom: 7px;
}

.sales-contact-card p, .sales-contact-card a {
  color: #00736c;
  font-size: 1rem;
  margin-bottom: 0;
  text-decoration: none;
}

.sales-contact-card a:hover {
  color: #f8972a;
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 900px) {
  .sales-container {
    flex-direction: column;
    gap: 18px;
    padding-top: 80px;
  }
  .sales-content {
    margin-bottom: 0;
  }
  .sales-aside {
    flex-direction: row;
    gap: 18px;
  }
  .sales-card, .sales-contact-card {
    flex: 1;
    min-width: 210px;
  }
}

@media (max-width: 650px) {
  .sales-container {
    padding: 60px 6px 40px 6px;
  }
  .sales-content, .sales-card, .sales-contact-card {
    padding: 15px 6px;
    border-radius: 10px;
  }
  .sales-title {
    font-size: 1.4rem;
  }
  .sales-card, .sales-contact-card {
    font-size: 0.95rem;
  }
}

@media (max-width: 500px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  .sales-aside {
    flex-direction: column;
    gap: 10px;
  }
  .sales-section {
    min-height: unset;
  }
}
