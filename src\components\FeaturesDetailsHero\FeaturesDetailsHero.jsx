// FeaturesDetailsHero.jsx
import React from 'react';
import './FeaturesDetailsHero.css';
import Header from '../Header/Header';
import PropTypes from 'prop-types';

const FeaturesDetailsHero = ({ featureData }) => {
  // Split the hero title into lines if it contains a period
  const titleLines = featureData.heroTitle.split('.');

  return (
    <section className="celaeno-hero-section">
      <Header />
      <div className="celaeno-hero-container">
        <h1 className="celaeno-hero-title">
          {titleLines.map((line, index) => (
            line.trim() && (
              <div key={index} className="celaeno-hero-title-line">
                {line.trim()}{index < titleLines.length - 1 ? '.' : ''}
              </div>
            )
          ))}
        </h1>

        <p className="celaeno-hero-description">
          {featureData.description}
        </p>
        <div className="cta-container">
          <button className="lets-go-btn">
            Sign Up
          </button>
          <p className="free-trial-text">Start your free trial - no credit card needed!</p>
        </div>
      </div>
    </section>
  );
};

FeaturesDetailsHero.propTypes = {
  featureData: PropTypes.object.isRequired
};

export default FeaturesDetailsHero;
