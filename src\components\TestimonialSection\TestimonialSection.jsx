import React, { useState, useEffect } from 'react';
import './TestimonialSection.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight, faQuoteRight } from '@fortawesome/free-solid-svg-icons';

const TestimonialSection = () => {
  // Dummy testimonial data
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Marketing Director, TechCorp",
      text: "<PERSON><PERSON><PERSON> has completely transformed how we manage client relationships. The automation features have saved us countless hours, and our client satisfaction scores have increased by 35% since implementation.",
      avatar: "https://randomuser.me/api/portraits/women/32.jpg"
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "CEO, Design Studio",
      text: "As a small design agency, we needed a solution that wouldn't break the bank but could still handle our growing client base. <PERSON><PERSON><PERSON> delivered exactly what we needed and more. The interface is intuitive and our team was up and running in days.",
      avatar: "https://randomuser.me/api/portraits/men/54.jpg"
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Project Manager, ConsultCo",
      text: "The client onboarding process used to take us weeks. With <PERSON><PERSON><PERSON>, we've streamlined everything down to days. Our clients love the professional experience, and we love the efficiency. It's a win-win!",
      avatar: "https://randomuser.me/api/portraits/women/68.jpg"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-rotation effect
  useEffect(() => {
    let interval;
    
    if (isAutoPlaying) {
      interval = setInterval(() => {
        const isLastTestimonial = currentIndex === testimonials.length - 1;
        const newIndex = isLastTestimonial ? 0 : currentIndex + 1;
        setCurrentIndex(newIndex);
      }, 3000); // Change every 3 seconds
    }
    
    // Clean up interval on unmount or when auto-play is disabled
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [currentIndex, isAutoPlaying, testimonials.length]);

  // Pause auto-rotation when user interacts with controls
  const handleUserInteraction = () => {
    setIsAutoPlaying(false);
    
    // Resume auto-rotation after 10 seconds of inactivity
    const timeout = setTimeout(() => {
      setIsAutoPlaying(true);
    }, 10000);
    
    return () => clearTimeout(timeout);
  };

  const goToPrevious = () => {
    handleUserInteraction();
    const isFirstTestimonial = currentIndex === 0;
    const newIndex = isFirstTestimonial ? testimonials.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    handleUserInteraction();
    const isLastTestimonial = currentIndex === testimonials.length - 1;
    const newIndex = isLastTestimonial ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = (index) => {
    handleUserInteraction();
    setCurrentIndex(index);
  };

  return (
    <section className="testimonial-section">
      <div className="quote-icon">
        <FontAwesomeIcon icon={faQuoteRight} />
      </div>
      
      <div className="testimonial-container">
        <button className="nav-button prev" onClick={goToPrevious}>
          <FontAwesomeIcon icon={faChevronLeft} />
        </button>

        <div className="testimonial-card">
          <h2>Testimony</h2>
          <div className="testimonial-content">
            <p className="testimonial-text">{testimonials[currentIndex].text}</p>
            
            <div className="testimonial-author">
              <img 
                src={testimonials[currentIndex].avatar} 
                alt={testimonials[currentIndex].name} 
                className="author-avatar"
              />
              <div className="author-info">
                <h4>{testimonials[currentIndex].name}</h4>
                <p>{testimonials[currentIndex].position}</p>
              </div>
            </div>
          </div>
        </div>

        <button className="nav-button next" onClick={goToNext}>
          <FontAwesomeIcon icon={faChevronRight} />
        </button>
      </div>
      
      <div className="testimonial-indicators">
        {testimonials.map((_, index) => (
          <span 
            key={index} 
            className={`indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>
    </section>
  );
};

export default TestimonialSection;
