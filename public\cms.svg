<svg width="247" height="213" viewBox="0 0 247 213" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_474_964)">
<circle cx="76.5" cy="191.5" r="21.5" fill="url(#paint0_linear_474_964)"/>
</g>
<g filter="url(#filter1_i_474_964)">
<circle cx="132.5" cy="16.5" r="16.5" fill="url(#paint1_linear_474_964)"/>
</g>
<circle cx="48.5" cy="91.5" r="6.5" fill="url(#paint2_linear_474_964)"/>
<circle cx="115.5" cy="153.5" r="6.5" fill="url(#paint3_linear_474_964)"/>
<g filter="url(#filter2_i_474_964)">
<path d="M98.7727 129.895C94.0795 129.895 90.0426 128.901 86.6619 126.912C83.3011 124.903 80.7159 122.119 78.9062 118.56C77.1165 115 76.2216 110.903 76.2216 106.27C76.2216 101.577 77.1264 97.4602 78.9361 93.9205C80.7656 90.3608 83.3608 87.5866 86.7216 85.598C90.0824 83.5895 94.0795 82.5852 98.7131 82.5852C102.71 82.5852 106.21 83.3111 109.213 84.7628C112.216 86.2145 114.592 88.2528 116.342 90.8778C118.092 93.5028 119.057 96.5852 119.236 100.125H107.244C106.906 97.8381 106.011 95.9986 104.56 94.6065C103.128 93.1946 101.249 92.4886 98.9219 92.4886C96.9531 92.4886 95.233 93.0256 93.7614 94.0994C92.3097 95.1534 91.1761 96.6946 90.3608 98.723C89.5455 100.751 89.1378 103.207 89.1378 106.091C89.1378 109.014 89.5355 111.5 90.331 113.548C91.1463 115.597 92.2898 117.158 93.7614 118.232C95.233 119.305 96.9531 119.842 98.9219 119.842C100.374 119.842 101.676 119.544 102.83 118.947C104.003 118.351 104.967 117.486 105.723 116.352C106.499 115.199 107.006 113.817 107.244 112.206H119.236C119.037 115.706 118.082 118.788 116.372 121.453C114.682 124.098 112.345 126.166 109.362 127.658C106.379 129.149 102.849 129.895 98.7727 129.895ZM127.372 129V83.1818H139.483V91.2656H140.02C140.974 88.581 142.565 86.4631 144.792 84.9119C147.02 83.3608 149.684 82.5852 152.787 82.5852C155.929 82.5852 158.603 83.3707 160.811 84.9418C163.018 86.4929 164.49 88.6009 165.226 91.2656H165.703C166.637 88.6406 168.328 86.5426 170.774 84.9716C173.24 83.3807 176.153 82.5852 179.514 82.5852C183.789 82.5852 187.26 83.9474 189.924 86.6719C192.609 89.3764 193.951 93.2145 193.951 98.1861V129H181.274V100.692C181.274 98.1463 180.598 96.2372 179.245 94.9645C177.893 93.6918 176.203 93.0554 174.174 93.0554C171.868 93.0554 170.068 93.7912 168.775 95.2628C167.483 96.7145 166.836 98.6335 166.836 101.02V129H154.517V100.423C154.517 98.1761 153.87 96.3864 152.578 95.054C151.305 93.7216 149.625 93.0554 147.537 93.0554C146.125 93.0554 144.852 93.4134 143.718 94.1293C142.605 94.8253 141.72 95.8097 141.064 97.0824C140.407 98.3352 140.079 99.8068 140.079 101.497V129H127.372ZM242.111 96.2472L230.478 96.9631C230.279 95.9687 229.851 95.0739 229.195 94.2784C228.539 93.4631 227.674 92.8168 226.6 92.3395C225.546 91.8423 224.283 91.5938 222.811 91.5938C220.843 91.5938 219.182 92.0114 217.83 92.8466C216.478 93.6619 215.801 94.7557 215.801 96.1278C215.801 97.2216 216.239 98.1463 217.114 98.902C217.989 99.6577 219.49 100.264 221.618 100.722L229.911 102.392C234.365 103.307 237.686 104.778 239.874 106.807C242.061 108.835 243.155 111.5 243.155 114.801C243.155 117.804 242.27 120.439 240.5 122.706C238.75 124.973 236.344 126.743 233.282 128.016C230.239 129.268 226.729 129.895 222.752 129.895C216.686 129.895 211.854 128.632 208.255 126.107C204.675 123.561 202.577 120.101 201.961 115.726L214.459 115.07C214.837 116.919 215.752 118.331 217.203 119.305C218.655 120.26 220.515 120.737 222.782 120.737C225.009 120.737 226.799 120.31 228.151 119.455C229.523 118.58 230.219 117.456 230.239 116.084C230.219 114.93 229.732 113.986 228.777 113.25C227.823 112.494 226.351 111.918 224.363 111.52L216.428 109.939C211.953 109.044 208.623 107.493 206.435 105.286C204.267 103.078 203.184 100.264 203.184 96.8438C203.184 93.9006 203.979 91.3651 205.57 89.2372C207.181 87.1094 209.438 85.4688 212.341 84.3153C215.265 83.1619 218.685 82.5852 222.603 82.5852C228.39 82.5852 232.944 83.8082 236.265 86.2543C239.605 88.7003 241.554 92.0312 242.111 96.2472Z" fill="url(#paint4_linear_474_964)"/>
</g>
<g filter="url(#filter3_i_474_964)">
<path d="M105 207C144.835 207 179.23 183.708 195.309 150M105 12C49.7715 12 5 56.7715 5 112C5 142.884 21.5001 169.157 43.5 187.5M193.554 65.5C185.2 49.6245 172.703 36.2695 157.5 26.8734" stroke="url(#paint5_linear_474_964)" stroke-width="10"/>
</g>
<g filter="url(#filter4_i_474_964)">
<path d="M99 154C71.3858 154 49 131.614 49 104M136.564 71C127.401 60.5776 113.969 54 99 54C79.4788 54 62.5705 65.1871 54.3365 81.5" stroke="url(#paint6_linear_474_964)" stroke-width="8"/>
</g>
<defs>
<filter id="filter0_i_474_964" x="55" y="170" width="45" height="47.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="6"/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_474_964"/>
</filter>
<filter id="filter1_i_474_964" x="116" y="0" width="35" height="37.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="6"/>
<feGaussianBlur stdDeviation="2.4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_474_964"/>
</filter>
<filter id="filter2_i_474_964" x="76.2207" y="82.5859" width="168.934" height="51.0086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="1.85"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_474_964"/>
</filter>
<filter id="filter3_i_474_964" x="0" y="7" width="203.822" height="209.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="6"/>
<feGaussianBlur stdDeviation="2.3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.36 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_474_964"/>
</filter>
<filter id="filter4_i_474_964" x="45" y="50" width="97.5684" height="111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.34 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_474_964"/>
</filter>
<linearGradient id="paint0_linear_474_964" x1="76.5" y1="170" x2="76.5" y2="213" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint1_linear_474_964" x1="132.5" y1="0" x2="132.5" y2="33" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint2_linear_474_964" x1="48.5" y1="85" x2="48.5" y2="98" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint3_linear_474_964" x1="115.5" y1="147" x2="115.5" y2="160" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint4_linear_474_964" x1="160" y1="47" x2="160" y2="149" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint5_linear_474_964" x1="100.154" y1="12" x2="100.154" y2="212" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
<linearGradient id="paint6_linear_474_964" x1="92.7821" y1="54" x2="92.7821" y2="154" gradientUnits="userSpaceOnUse">
<stop offset="0.284" stop-color="#FFAB6A"/>
<stop offset="1" stop-color="#007474"/>
</linearGradient>
</defs>
</svg>
