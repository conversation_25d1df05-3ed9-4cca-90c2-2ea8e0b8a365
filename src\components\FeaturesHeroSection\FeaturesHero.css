
.features-hero-section {
  width: 100%;
  padding: 20px 25px;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.features-hero-container {
  max-width: 2000px;
  margin: 0 auto;
  background-color: #ede2cd70;
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.05);
}

.features-hero-title {
  font-size: 36px;
  line-height: 1.3;
  margin-bottom: 40px;
  font-weight: 700;
}

.features-hero-orange-text {
  color: #f8972a;
}

.features-hero-gradient-text {
  background: linear-gradient(90deg, #f8972a, #00736c);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #00736c; /* Fallback */
}

.features-hero-description {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  max-width: 800px;
  margin: 0 auto 30px;
}

.cta-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -3rem;
  margin-bottom: 20px;
}

.cta-container-home {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  margin-top: -2rem;
}

.free-trial-text {
  margin-top: 10px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .features-hero-container {
    padding: 40px 30px;
  }

  .features-hero-title {
    font-size: 30px;
    margin-bottom: 30px;
  }

  .features-hero-description {
    font-size: 15px;
    margin-bottom: 40px;
  }
}

@media (max-width: 576px) {
  .features-hero-section {
    padding: 60px 15px;
  }

  .features-hero-container {
    padding: 30px 20px;
  }

  .features-hero-title {
    font-size: 26px;
    margin-bottom: 25px;
  }

  .features-hero-description {
    font-size: 14px;
    margin-bottom: 30px;
  }

  .free-trial-text {
    font-size: 13px;
  }
}
