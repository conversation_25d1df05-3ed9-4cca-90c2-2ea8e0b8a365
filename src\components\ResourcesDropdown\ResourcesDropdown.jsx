import React from 'react';
import { Link } from 'react-router-dom';
import './ResourcesDropdown.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBook,
  faLifeRing,
  faComments,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';

const ResourcesDropdown = ({ isOpen, onMouseEnter, onMouseLeave }) => {
  const resourceItems = [
    {
      id: 'knowledge-base',
      title: 'Knowledge Base',
      icon: faBook,
      hasLink: true,
      link: 'https://helpdesk-new.celaenotechnology.com/knowledgebase.php',
      isExternal: true
    },
    {
      id: 'support',
      title: 'Support',
      icon: faLifeRing,
      hasLink: true,
      link: 'https://helpdesk-new.celaenotechnology.com/index.php',
      isExternal: true
    },
    {
      id: 'talk-to-sales',
      title: 'Talk to Sales',
      icon: faComments,
      hasLink: true,
      link: '/Talktosales',
      isExternal: false
    }
  ];

  const handleItemClick = (onMouseLeave) => {
    // Close the dropdown when any item is clicked
    onMouseLeave();
  };

  return (
    <div
      className={`resources-dropdown ${isOpen ? 'open' : ''}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="resources-dropdown-container">
        <div className="resources-dropdown-content">
          <ul className="resources-list">
            {resourceItems.map((item) => (
              <li key={item.id}>
                {item.hasLink ? (
                  item.isExternal ? (
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="resource-link"
                      onClick={() => handleItemClick(onMouseLeave)}
                    >
                      <div className="resource-icon">
                        <FontAwesomeIcon icon={item.icon} />
                      </div>
                      <span>{item.title}</span>
                      <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                    </a>
                  ) : (
                    <Link
                      to={item.link}
                      className="resource-link"
                      onClick={() => handleItemClick(onMouseLeave)}
                    >
                      <div className="resource-icon">
                        <FontAwesomeIcon icon={item.icon} />
                      </div>
                      <span>{item.title}</span>
                      <FontAwesomeIcon icon={faArrowRight} className="arrow-icon" />
                    </Link>
                  )
                ) : (
                  <div
                    className="resource-link disabled"
                    onClick={() => handleItemClick(onMouseLeave)}
                  >
                    <div className="resource-icon">
                      <FontAwesomeIcon icon={item.icon} />
                    </div>
                    <span>{item.title}</span>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ResourcesDropdown;