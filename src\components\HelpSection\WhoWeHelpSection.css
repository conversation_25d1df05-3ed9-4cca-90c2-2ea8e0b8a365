/* ConnectSection.css */
.connect-section {
  width: 100%;
  padding: 80px 20px;
  background-color: #fff8f2;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* Bubble decorations */
.bubble {
  position: absolute;
  border-radius: 50%;
  background-color: #eaad77;
  opacity: 0.7;
  z-index: 1;
}

.bubble-1 {
  width: 100px;
  height: 120px;
  left: -60px;
  top: 20%;
}
/* 
.bubble-2 {
  width: 80px;
  height: 80px;
  left: 5%;
  bottom: 10%;
} */

.bubble-3 {
  width: 120px;
  height: 120px;
  right: -40px;
  top: 10%;
}

.bubble-4 {
  width: 70px;
  height: 70px;
  right: 2%;
  bottom: 47%;
}

/* Background circles */
.connect-bg-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.connect-circle {
  position: absolute;
  border-radius: 50%;
  background-color: #eaad77;
  opacity: 0.6;
}

.connect-circle-1 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -50px;
}

.connect-circle-2 {
  width: 250px;
  height: 250px;
  top: -50px;
  right: -100px;
}

/* Main content */
.connect-container {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.connect-title {
  color: #00736c;
  font-size: 36px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.connect-description {
  font-size: 18px;
  color: #333;
  margin: 0 auto 60px;
  max-width: 700px;
}

/* Arc container with fixed positioned icons */
.connect-arc-container {
  position: relative;
  height: 30px;
  margin: 90px auto 40px;
}

/* The semi-circle arc */
.connect-arc {
  position: absolute;
  width: 100%;
  height: 850px; /* Double height to create semi-circle */
  border: 2px solid #f8972a;
  border-bottom: none;
  border-radius: 60% 60% 0 0;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

/* Icons container */
.connect-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* Icon wrapper */
.connect-icon-wrapper {
  position: absolute;
  width: 60px;
  height: 60px;
  background-color: #00736c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 115, 108, 0.2);
  transform: translate(-50%, -50%);
  z-index: 3;
}

.connect-icon {
  color: white;
  font-size: 24px;
}

/* Position icons on the arc */
.connect-icon-1 {
  left: 24%;
  top: 200%;
}

.connect-icon-2 {
  left: 40%;
  top: 20%;
}

.connect-icon-3 {
  left: 60%;
  top: 10%;
}

.connect-icon-4 {
  left: 75%;
  top: 200%;
}

/* Labels for each step */
.connect-label {
  color: #f8972a;
  font-size: 14px;
  font-weight: 500;
  position: absolute;
  width: max-content;
  text-align: center;
  white-space: nowrap;
}

/* Position labels based on their position on the arc */
.connect-icon-1 .connect-label {
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.connect-icon-2 .connect-label {
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.connect-icon-3 .connect-label {
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.connect-icon-4 .connect-label {
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}

/* Arrows between steps */
.connect-arrow {
  position: absolute;
  height: 2px;
  background-color: #f8972a;
  width: 80px; /* Length of the arrow */
  z-index: 1;
}

.connect-arrow::after {
  content: '';
  position: absolute;
  right: 0;
  top: -4px;
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #f8972a;
}

/* Position and rotate arrows based on their position on the arc */
.connect-arrow-1 {
  width: 70px;
  transform: rotate(-25deg);
  top: 10px;
  left: 50px;
}

.connect-arrow-2 {
  width: 90px;
  transform: rotate(0deg);
  top: 30px;
  left: 50px;
}

.connect-arrow-3 {
  width: 70px;
  transform: rotate(15deg);
  top: 50px;
  left: 50px;
}

/* Responsive styles */
@media (max-width: 992px) {
  .connect-arc-container {
    height: 30px;
  }

  .connect-arc {
    height: 860px;
  }

  .connect-icon-wrapper {
    width: 55px;
    height: 55px;
  }

  .connect-icon {
    font-size: 22px;
  }

  .connect-arrow {
    width: 60px;
  }
  .connect-arrow-1 {
    width: 60px;
    top: 10px;
    left: 40px;
  }

  .connect-arrow-2 {
    width: 70px;
    top: 20px;
    left: 40px;
  }

  .connect-arrow-3 {
    width: 60px;
    top: 40px;
    left: 40px;
  }
}

@media (max-width: 768px) {
  .connect-section {
    padding: 60px 20px;
  }

  .connect-title {
    font-size: 30px;
  }

  .connect-description {
    font-size: 16px;
    margin-bottom: 40px;
  }

  .connect-arc-container {
    height: 20px;
  }

  .connect-arc {
    height: 400px;
  }

  .connect-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .connect-icon {
    font-size: 20px;
  }

  .connect-label {
    font-size: 13px;
  }

  .connect-arrow {
    width: 50px;
  }

  /* Adjust icon positions for smaller screens */
  .connect-icon-1 {
    left: 20%;
  }

  .connect-icon-4 {
    left: 80%;
  }
}

/* Mobile layout - convert to vertical flow */
@media (max-width: 576px) {
  .connect-section {
    padding: 60px 20px;
  }

  .connect-title {
    font-size: 30px;
  }

  .connect-description {
    font-size: 16px;
    margin-bottom: 40px;
  }

  .connect-arc-container {
    height: 20px;
  }

  .connect-arc {
    height: 400px;
  }

  .connect-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .connect-icon {
    font-size: 20px;
  }

  .connect-label {
    font-size: 10px;

  }

  .connect-arrow {
    width: 40px;
  }

  /* Adjust icon positions for smaller screens */
  .connect-icon-1 {
    left: 15%;
    top: 300%;
  }
.connect-icon-2 {
    left: 35%;
    top: 70%;
  }

  .connect-icon-3 {
    left: 60%;
    top: 30%;
  }

  .connect-icon-4 {
    left: 85%;
    top: 300%;
  }
}
