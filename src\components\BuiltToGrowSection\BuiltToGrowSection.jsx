import './BuiltToGrowSection.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faBuilding } from '@fortawesome/free-solid-svg-icons';

const CelaenoGrowthSection = () => {
  return (
    <section className="celaeno-growth-section">
      {/* Bubble decorations */}
      <div className="celaeno-bubble celaeno-bubble-1"></div>
      <div className="celaeno-bubble celaeno-bubble-2"></div>
      <div className="celaeno-bubble celaeno-bubble-3"></div>
      <div className="celaeno-bubble celaeno-bubble-4"></div>

      <div className="celaeno-growth-container">
        <h2 className="celaeno-growth-title">Built to Grow With You</h2>
        <p className="celaeno-growth-description">
          Whether you're a solo freelancer or a growing team, <PERSON>laeno adapts to your pace.
        </p>

        <div className="celaeno-arc-container">
          <div className="celaeno-arc"></div>

          <div className="celaeno-business-types">
            <div className="celaeno-business-type celaeno-business-type-1">
              <span className="celaeno-business-label">freelancer</span>
              <div className="celaeno-icon-wrapper celaeno-icon-1">
                <FontAwesomeIcon icon={faHome} />
              </div>
            </div>

            <div className="celaeno-business-type celaeno-business-type-2">
              <div className="celaeno-icon-wrapper celaeno-icon-2">
                <FontAwesomeIcon icon={faBuilding} />
              </div>
              <span className="celaeno-business-label">architecture studio</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CelaenoGrowthSection;
