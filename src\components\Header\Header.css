/* Header.css */
.hero-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  position: fixed; /* Changed from relative to fixed */
  top: 25px;
  left: 25px;
  right:25px;
  border-radius: 20px;
  padding: 20px;
  z-index: 1000;
  transition: all 0.3s ease;
  background-color: transparent; /* Start with transparent background */
}

/* Add margin to the first element after the header to prevent overlap */
.hero-header + * {
  margin-top: 90px;
}

/* Sticky header styles */
.hero-header.sticky {
  /* background-color: rgba(255, 255, 255, 0.95); Semi-transparent white */
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  backdrop-filter: blur(10px); /* Modern blur effect */
  -webkit-backdrop-filter: blur(10px); /* For Safari */
  padding: 12px 20px; /* Slightly smaller padding when sticky */
}

/* Adjust link colors for better visibility when sticky */
.hero-header.sticky .nav-links li {
  color: #333;
}

.hero-header.sticky .hamburger-menu {
  color: #333;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: opacity 0.3s ease;
  height: 100%;
  padding: 0;
  margin-right: 20px;
}

.logo-link:hover {
  opacity: 0.9;
}

.logo {
  height: 50px; /* Adjust based on your SVG's desired height */
  width: auto;
  max-width: 150px; /* Adjust based on your SVG's width */
  display: block;
  transition: height 0.3s ease; /* Smooth transition for logo size */
}

.sticky .logo {
  height: 45px; /* Slightly smaller logo when sticky */
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
}

.nav-links {
  display: flex;
  gap: 30px;
  list-style: none;
  padding: 0;
  margin: 0;
  font-weight: 600;
  transition: color 0.3s ease;
}

.nav-links li {
  cursor: pointer;
  transition: color 0.3s ease, transform 0.2s ease;
  position: relative;
}

.nav-links li:hover {
  color: #00736c;
  transform: translateY(-2px);
}

/* Modern underline effect on hover and active state */
.nav-links > li:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #00736c;
  transition: width 0.3s ease;
}

.nav-links > li:hover:after {
  width: 100%;
}

/* Active page underline */
.nav-links > li.active:after {
  width: 100%;
  background-color: #00736c;
}

/* Active link text color */
.nav-links > li.active > a {
  color: #00736c;
  font-weight: 600;
}

/* Features dropdown styling */
.features-link {
  position: relative; /* Changed from static to relative for proper positioning */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.features-link a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  position: relative;
}

/* Resources dropdown styling */
.resources-link {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.resources-link a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  position: relative;
  z-index: 1001;
}

.dropdown-icon {
  font-size: 12px;
  margin-left: 5px;
  transition: transform 0.3s ease;
  color: inherit;
}

.features-link.active .dropdown-icon,
.resources-link.active .dropdown-icon {
  transform: rotate(180deg);
}

@media (max-width: 992px) {
  .features-link,
  .resources-link {
    position: relative;
    width: 100%;
  }

  .features-link a,
  .resources-link a {
    width: 100%;
    justify-content: space-between;
  }
}

.hero-nav-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.contact-btn {
  background: #ffab6a;
  border: 1px solid #ffab6a;
  padding: 8px 16px;
  border-radius: 999px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.contact-btn:hover, .contact-btn.active {
  background: #fff;
  color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
}

.contact-btn.active a {
  font-weight: 600;
  color: #de7c20;
}

.login-btn {
  background: transparent;
  border: 1px solid #007474;
  border-radius: 35px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background-color: rgba(0, 115, 108, 0.1);
  border-color: #007474;
  color: #007474;
  transform: translateY(-2px);
}

.signup-btn {
  background-color: #007474;
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 38px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 115, 108, 0.2);
}

.signup-btn:hover {
  background-color: #005b55;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 115, 108, 0.3);
}

/* Hamburger menu icon - hidden by default */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hamburger-menu {
  display: none;
  font-size: 20px; /* Slightly smaller font size */
  cursor: pointer;
  color: #00736c;
  z-index: 1001; /* Above the nav-container */
  position: absolute;
  right: 25px;
  top: 9px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: transparent; /* No background by default */
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hamburger-menu:hover {
  transform: scale(1.1);
  background-color: transparent; /* No background on hover */
}

.hamburger-menu.active {
  background-color: rgba(0, 115, 108, 0.2); /* Background only when active (X is showing) */
  color: #005b55;
}

.menu-icon {
  transition: all 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.hamburger-menu.active .menu-icon {
  transform: rotate(0deg); /* No rotation needed for X icon */
}

@keyframes slideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleUp {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@media (max-width: 992px) {
  .hamburger-menu {
    display: flex;
  }

  /* Mobile menu backdrop */
  .nav-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: -1;
  }

  .nav-container.active::before {
    opacity: 1;
    visibility: visible;
  }

  .nav-container {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 400px;
    height: 100vh;
    background-color: #ffffff;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 80px 30px 30px;
    box-shadow: -5px 0 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
    z-index: 1000;
    border-radius: 0 0 0 20px;
    overflow-y: auto;
    border-left: 1px solid rgba(0, 0, 0, 0.05);
    visibility: hidden; /* Hide the container when not active */
    pointer-events: none; /* Disable interactions when not active */
  }

  .nav-container.active {
    right: 0;
    animation: slideIn 0.4s forwards;
    visibility: visible; /* Show the container when active */
    pointer-events: auto; /* Enable interactions when active */
  }

  .hero-nav-left {
    width: 100%;
    margin-bottom: 40px;
  }

  .nav-links {
    flex-direction: column;
    gap: 25px;
    width: 100%;
  }

  .nav-container.active .nav-links li {
    opacity: 0;
    transform: translateX(20px);
    animation: fadeIn 0.5s forwards;
    position: relative;
  }

  .nav-container.active .nav-links li::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00736c;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .nav-container.active .nav-links li:hover::before {
    opacity: 1;
  }

  .nav-container.active .nav-links li:nth-child(1) { animation-delay: 0.1s; }
  .nav-container.active .nav-links li:nth-child(2) { animation-delay: 0.2s; }
  .nav-container.active .nav-links li:nth-child(3) { animation-delay: 0.3s; }
  .nav-container.active .nav-links li:nth-child(4) { animation-delay: 0.4s; }

  .nav-links li {
    font-size: 18px;
    padding: 5px 0;
    color: #333333;
  }

  .nav-links li:hover {
    color: #00736c;
  }

  .nav-links > li:after {
    bottom: -2px;
    height: 3px;
    background-color: #00736c;
  }

  .hero-nav-right {
    width: 100%;
    flex-direction: column;
    gap: 15px;
  }

  .nav-container.active .hero-nav-right button {
    opacity: 0;
    transform: scale(0.9);
    animation: scaleUp 0.4s forwards;
  }

  .nav-container.active .hero-nav-right button:nth-child(1) { animation-delay: 0.5s; }
  .nav-container.active .hero-nav-right button:nth-child(2) { animation-delay: 0.6s; }
  .nav-container.active .hero-nav-right button:nth-child(3) { animation-delay: 0.7s; }

  .hero-nav-right button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .hero-nav-right button:hover {
    transform: translateY(-3px);
  }
}

@media (max-width: 480px) {
  .nav-container {
    width: 100%;
    border-radius: 0;
  }

  .hero-header {
    margin-top: 45px;
  }

  .logo-link {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .hamburger-menu {
    display: flex;
    right: 20px;
    top: 10px;
    width: 36px;
    height: 36px;
  }

  Enhance animations for smaller screens
  .nav-container.active .nav-links li {
    animation-duration: 0.4s;
  }

  .nav-container.active .hero-nav-right button {
    animation-duration: 0.35s;
  }
  .features-link,
  .resources-link {
     display: block;
  }
}

@media (max-width: 768px) {

    .hero-header {
    margin-top: 35px;
  }
  .hero-header.sticky {
    padding: 10px 15px;
  }

  .logo {
    height: 40px;
  }
  .sticky .logo {
    height: 36px;
  }

   .hamburger-menu {
    display: flex;
        margin-top: 12px;
  }
  .logo-link {
    margin-right: 500px;
    margin-top: 10px;
  }
}

@media (max-width: 375px) {
  .hero-header {
    margin-top: 45px;
  }

  .logo {
    height: 26px;
  }

  .sticky .logo {
    height: 22px;
  }

  .hamburger-menu {
    display: flex;
  }
}
