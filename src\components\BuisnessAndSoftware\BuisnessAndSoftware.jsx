import React from 'react';
import './BuisnessAndSoftware.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faCloud, faBuilding } from '@fortawesome/free-solid-svg-icons';

const BuisnessAndSoftware = () => {
  const distributionModels = [
    {
      icon: faUsers,
      title: "B2B / B2C / C2C",
      description: "Custom-built software designed to support businesses of all sizes, from startups to large enterprises."
    },
    {
      icon: faCloud,
      title: "SaaS",
      description: "Scalable and flexible cloud-based or self-hosted solutions, making industry-wide software accessible anytime, anywhere."
    },
    {
      icon: faBuilding,
      title: "On-Premises",
      description: "Secure, autonomous software solutions that give you full control over your data and integrations."
    }
  ];

  return (
    <section className="business-software-section">
      <div className="business-software-container">
        <div className="business-software-header">
          <h2 className="business-software-title">
            Business and <span className="highlight-text">Software</span> Distribution Models
          </h2>
        </div>

        <div className="distribution-models">
          {distributionModels.map((model, index) => (
            <div key={index} className="distribution-card">
              <div className="distribution-icon">
                <FontAwesomeIcon icon={model.icon} />
              </div>
              <div className="distribution-content">
                <h3 className="distribution-title">{model.title}</h3>
                <p className="distribution-description">{model.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BuisnessAndSoftware;