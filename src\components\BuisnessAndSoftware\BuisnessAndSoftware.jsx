import React from 'react';
import './BuisnessAndSoftware.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGlobe,
  faMobileAlt,
  faCode,
  faShoppingCart,
  faPaintBrush,
  faRobot
} from '@fortawesome/free-solid-svg-icons';

const BuisnessAndSoftware = () => {
  const services = [
    {
      icon: faGlobe,
      title: "Web Development",
      description: "Create responsive, fast, and user-friendly websites that drive business growth and enhance user experience."
    },
    {
      icon: faMobileAlt,
      title: "Mobile App Development",
      description: "Build native and cross-platform mobile applications for iOS and Android with cutting-edge technology."
    },
    {
      icon: faCode,
      title: "Custom Software Development",
      description: "Develop tailored software solutions that meet your specific business requirements and objectives."
    },
    {
      icon: faShoppingCart,
      title: "E-commerce Solutions",
      description: "Build powerful online stores with secure payment gateways and inventory management systems."
    },
    {
      icon: faPaintBrush,
      title: "UI/UX Design",
      description: "Design intuitive and engaging user interfaces that provide exceptional user experiences."
    },
    {
      icon: faRobot,
      title: "AI & Machine Learning",
      description: "Implement intelligent solutions using artificial intelligence and machine learning technologies."
    }
  ];

  return (
    <section className="business-software-section">
      <div className="business-software-container">
        <div className="business-software-header">
          <h2 className="business-software-title">
            Business and Software Development Services
          </h2>
          <p className="business-software-subtitle">
            Comprehensive solutions to transform your business with cutting-edge technology and innovative approaches.
          </p>
        </div>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">
                <FontAwesomeIcon icon={service.icon} />
              </div>
              <div className="service-content">
                <h3 className="service-title">{service.title}</h3>
                <p className="service-description">{service.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BuisnessAndSoftware;