// ClientProfileCards.jsx
import React from 'react';
import './ClientProfileCards.css';
import PropTypes from 'prop-types';

const ClientProfileCards = ({ featureData }) => {
  // Transform the sections from the feature data into profile cards
  const profileCards = featureData.sections.map((section, index) => ({
    id: index + 1,
    color: index % 2 === 0 ? "teal" : "orange",
    title: section.title || featureData.title,
    description: section.description,
    bulletPoints: section.bulletPoints || []
  }));

  return (
    <section className="profile-section">
      <div className="profile-container">
        {profileCards.map((card) => (
          <div key={card.id} className={`profile-row ${card.id % 2 === 0 ? 'profile-row-even' : 'profile-row-odd'}`}>
            <div className={`profile-shape profile-shape-${card.color}`}></div>
            <div className="profile-card">
              <div className="profile-card-content">
                <h3 className={`profile-title profile-title-${card.color}`}>{card.title}</h3>
                <p className="profile-description">{card.description}</p>
                {card.bulletPoints.length > 0 && (
                  <ul className="profile-list">
                    {card.bulletPoints.map((point, index) => (
                      <li key={index} className="profile-list-item">{point}</li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

ClientProfileCards.propTypes = {
  featureData: PropTypes.object.isRequired
};

export default ClientProfileCards;
