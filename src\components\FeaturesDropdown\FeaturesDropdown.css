:root {
  --color-primary: #ffab6a;
  --transition: 0.3s ease;

}

.features-dropdown {
  position: absolute;
  width: 1000%;
  top: calc(100% + 25px); /* Increased space between header and dropdown */
  border-radius: 33px;
  padding: 25px 30px 50px 30px; /* Increased top padding */
  background-color: #042e33;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: var(--transition);
  height: auto;
  z-index: 1000;
  margin-top: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* Added shadow for better visibility */
}

.features-dropdown.open {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.features-dropdown-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  position: relative;
}

/* Create a bridge element to make it easier to move the mouse to the dropdown */
 .features-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 35px; /* Increased height of the bridge */
  bottom: -35px; /* Position it below the link */
  left: 0;
  z-index: 999;
  background-color: transparent; /* Invisible bridge */
}

.features-dropdown-container::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #042e33;
}

.features-dropdown-columns {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.features-column {
  width: 33%;
}

.column-title {
  width: max-content;
  margin-bottom: 25px;
  font-size: 16px;
  text-transform: uppercase;
  color: #ffab6a;
  font-weight: 600;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 295px;
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.features-list li {
  display: flex;
  
  margin-right: -100px;
  cursor: pointer;
  position: relative;
  margin-bottom: 0;
}

.feature-link {
  width: 70%;
  display: flex;
  align-items: center;
  padding: 6px 16px 6px 6px;
  gap: 16px;
  border-radius: 14px;
  transition: var(--transition);
  color: #ffffff;
  text-decoration: none;
  font-size: 15px;
}

.feature-link span {
  white-space: nowrap;
  color: #ffffff;
}

.feature-link:hover {
  background-color: rgba(255, 255, 255, 0.04);
  color: var(--color-primary);
}

.feature-link:hover span {
  color: var(--color-primary);
}

.arrow-icon {
  margin-left: 0;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.feature-link:hover .arrow-icon {
  opacity: 1;
  visibility: visible;
  color: var(--color-primary);
}

.feature-icon {
  display: flex;
  padding: 10px;
  border-radius: 5px;
  background-color: rgba(0, 116, 116, 0.1);
  color: #007474;
}

/* Different colored icons */
.features-list li:nth-child(1) .feature-icon {
  color: #0a99ab;
  background-color: rgba(231, 76, 60, 0.1);
}

.features-list li:nth-child(2) .feature-icon {
  color: #d77539;
  background-color: rgba(52, 152, 219, 0.1);
}

.features-list li:nth-child(3) .feature-icon {
  color: #9b59b6;
  background-color: rgba(155, 89, 182, 0.1);
}

.features-list li:nth-child(4) .feature-icon {
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
}

.features-list li:nth-child(5) .feature-icon {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.features-list li {
  animation: fadeIn 0.5s forwards;
  opacity: 0;
}

.features-list li:nth-child(1) { animation-delay: 0.05s; }
.features-list li:nth-child(2) { animation-delay: 0.1s; }
.features-list li:nth-child(3) { animation-delay: 0.15s; }
.features-list li:nth-child(4) { animation-delay: 0.2s; }
.features-list li:nth-child(5) { animation-delay: 0.25s; }
.features-list li:nth-child(6) { animation-delay: 0.3s; }

/* Responsive styles */
@media (max-width: 1280px) {
  .features-list {
    max-width: 250px;
  }
}

@media (max-width: 1024px) {
  .features-dropdown {
    position: absolute;
    border-radius: 20px;
    padding: 0;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition);
    margin-top: 0;
    box-shadow: none;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    z-index: -1; /* Ensure it's behind other elements when not active */
  }

  .features-dropdown.open {
    max-height: 2000px;
    overflow-y: auto;
    padding: 20px 0;
    margin-top: 10px;
    margin-bottom: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    z-index: 1000; /* Bring it to the front when active */
  }

  .features-dropdown-container::before {
    display: none;
  }

  .features-dropdown-columns {
    flex-direction: column;
    gap: 30px;
  }

  .features-column {
    width: 100%;
    margin-bottom: 10px;
  }

  .features-list {
    width: 100%;
    max-width: 100%;
    gap: 5px;
  }

  .features-list li {
    width: 100%;
    padding: 0;
    margin: 0;
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .features-dropdown-container {
    padding: 0 15px;
  }

  .column-title {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .feature-link {
    font-size: 14px;
    padding: 8px 12px 8px 6px;
  }

  .feature-icon {
    padding: 8px;
  }
}
