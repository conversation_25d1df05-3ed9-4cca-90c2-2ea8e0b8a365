
import './ScalableBusinessSection.css';
import { Link } from "react-router-dom";

const ScalableBusinessSection = () => {
  const businessTypes = [
    {
      type: 'Freelancers:',
      description: 'Manage clients and projects with ease',
      color: 'orange'
    },
    {
      type: 'Small Business:',
      description: 'Easy to use, budget-friendly',
      color: 'teal'
    },
    {
      type: 'Medium Business:',
      description: 'Powerful tools with team collaboration',
      color: 'orange'
    },
    {
      type: 'Large Organizations:',
      description: 'Custom workflows and API access',
      color: 'teal'
    }
  ];

  return (
    <section className="scalable-section">
      <h2 className="scalable-heading">Scalable for Every Business Size</h2>
      
      <div className="business-cards-container">
        {businessTypes.map((business, index) => (
          <div 
            key={index} 
            className={`business-card ${business.color}`}
          >
            <h3 className="business-type">{business.type}</h3>
            <p className="business-description">{business.description}</p>
          </div>
        ))}
      </div>
      <div className='cta-btn'>
        <a target='_blank' href='https://cms.celaenotechnology.com/register.php' className="get-started-btn">Get Started</a>
        <Link to="/Talktosales" className="talk-to-sales-btn">Talk to Sales</Link>
      </div>
    </section>
  );
};

export default ScalableBusinessSection;
