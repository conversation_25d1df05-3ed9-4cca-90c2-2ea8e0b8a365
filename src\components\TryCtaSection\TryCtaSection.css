
.try-cta-section {
  width: 100%;
  background: linear-gradient(135deg, #00736c, #2aa198);
  padding: 60px 20px;
  color: white;
  text-align: left;
  position: relative;
  overflow: hidden;
}

/* Left glow effect */
.glow-effect-left {
  position: absolute;
  top: -50%;
  left: -26%;
  width: 50%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(243, 242, 242, 0.733) 0%, rgba(255, 255, 255, 0.05) 40%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
  z-index: 1;
  transform: rotate(10deg);
}

/* Right glow effect */
.glow-effect-right {
  position: absolute;
  top: -50%;
  right: -25%;
  width: 50%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(245, 243, 243, 0.653) 0%, rgba(255, 255, 255, 0.05) 40%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
  z-index: 1;
  transform: rotate(-10deg);
}

.try-cta-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.try-cta-title {
  font-size: 36px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
}

.try-cta-subtitle {
  font-size: 18px;
  margin: 0 0 30px 0;
  font-weight: 400;
}

.try-cta-buttons {
  margin-top: -2rem;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.try-cta-trial-btn {
  margin-top: 30px;
    padding: 15px 40px;
    background: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;  
    transition: var(--transition);
    display: inline-block; 
}

.try-cta-trial-btn:hover {
  background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(77, 182, 172, 0.3);
}

.try-cta-call-btn {
    margin-top: 30px;
    padding: 15px 40px;
    background: var(--secondary-color);
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;  
    transition: var(--transition);
    display: inline-block; 
}

.try-cta-call-btn:hover {
  background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(77, 182, 172, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .try-cta-title {
    font-size: 30px;
  }

  .try-cta-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .try-cta-buttons {
    flex-direction: column;
    width: 100%;
  }

  .try-cta-trial-btn, .try-cta-call-btn {
    width: 100%;
  }
}
