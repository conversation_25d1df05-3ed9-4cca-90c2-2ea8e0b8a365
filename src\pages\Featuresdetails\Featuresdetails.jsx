import { useParams } from "react-router-dom";
import ClientProfileCards from "../../components/ClientProfilesSection/ClientProfileCards";
import ConnectSection from "../../components/ConnectSection/ConnectSection";
import FeaturesDetailsHero from "../../components/FeaturesDetailsHero/FeaturesDetailsHero";
import featuresData from "../../data/featuresData";
import TryCtaSection from "../../components/TryCtaSection/TryCtaSection";
import TestimonialSection from "../../components/TestimonialSection/TestimonialSection";

const Featuresdetails = () => {
  // Get the feature ID from the URL
  const { featureId } = useParams();

  // Find the feature data based on the ID
  const featureData = featuresData.find(feature => feature.id === parseInt(featureId)) || featuresData[0];

  return (
    <>
      <FeaturesDetailsHero featureData={featureData} />
      <ClientProfileCards featureData={featureData} />
      <ConnectSection />
      <TryCtaSection />
    </>
  );
};
export default Featuresdetails;